{"version": 3, "sources": ["main.js", "utils.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACxCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "app.bundle.js", "sourcesContent": ["// Main application JavaScript file\n(function() {\n    'use strict';\n    \n    console.log('Main application loaded');\n    \n    // Sample function that uses jQuery\n    function initializeApp() {\n        $(document).ready(function() {\n            console.log('App initialized with jQuery');\n            \n            // Sample event handler\n            $('body').on('click', '.btn-sample', function() {\n                alert('Sample button clicked!');\n            });\n            \n            // Sample AJAX call\n            $('.load-data').on('click', function() {\n                $.ajax({\n                    url: '/api/data',\n                    method: 'GET',\n                    success: function(data) {\n                        console.log('Data loaded:', data);\n                    },\n                    error: function() {\n                        console.log('Error loading data');\n                    }\n                });\n            });\n        });\n    }\n    \n    // Initialize the app\n    initializeApp();\n    \n    // Export for global access\n    window.App = {\n        init: initializeApp\n    };\n})();\n", "// Utility functions for the application\n(function() {\n    'use strict';\n    \n    // Utility object\n    var Utils = {\n        // Format currency\n        formatCurrency: function(amount) {\n            return '$' + parseFloat(amount).toFixed(2);\n        },\n        \n        // Validate email\n        validateEmail: function(email) {\n            var re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            return re.test(email);\n        },\n        \n        // Show notification using jQuery\n        showNotification: function(message, type) {\n            type = type || 'info';\n            var $notification = $('<div class=\"notification notification-' + type + '\">' + message + '</div>');\n            $('body').append($notification);\n            \n            setTimeout(function() {\n                $notification.fadeOut(function() {\n                    $(this).remove();\n                });\n            }, 3000);\n        },\n        \n        // Debounce function\n        debounce: function(func, wait) {\n            var timeout;\n            return function executedFunction() {\n                var context = this;\n                var args = arguments;\n                var later = function() {\n                    timeout = null;\n                    func.apply(context, args);\n                };\n                clearTimeout(timeout);\n                timeout = setTimeout(later, wait);\n            };\n        }\n    };\n    \n    // Export to global scope\n    window.Utils = Utils;\n})();\n"]}