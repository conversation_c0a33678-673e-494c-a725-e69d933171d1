{"GlobalPropertiesHash": "hgzcWlTcSK3QRbpzJ6lxtE3NxFoE+1MbrGfgOXGcmMg=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["SLNT8cloWz2hO830AafiLJz5zuKbnKyoOLdWdE269Oo=", "0CzU3v7wyBoivkT6eon7W5wN/R/N+9LZUIMjS+8eupE=", "W18W+air8HY2js46gQnZcix7gQ98kqjzCNhP+R6Ey34=", "dy5CE6pA0NsxyJDi79Vpy1X8grIaELFwJY5S1UKWC1Y=", "WUA6KKQuqLTOwef8JxdcKsu7tzujKRwnnJz2a0Fi8Ig=", "qZCT+WYBhhnFfDHICbUFZ8kKGXjr1wJqVBh2NeFoMfg=", "rs7nkmKf3819EnyxnTr4Wc4BP2X2HIyPuy86DjTWoE8=", "2iMBoaQzl5BWqaX+jFjnR0YwpMlmfIrg4vz6V22QS0k=", "0wetNwxFEOz1r1n9z5+2nOOJKDXyqLxo0l3CSx9CPiU=", "gEmsboixz3k7bOvmERsXYmQku2PJIkyo6zIxo6oqvHU=", "QT6tbgRF7MfuWLlGzOR+1nsOuaZ1mpHdLIFQnRxMBes=", "vVlAWhVcfxZJR+taj3raiiV7wekkAkMCaj9YLDXFSFM=", "KCIs7LbJyHWGAykpydXYGO1iUoGrIbPy2pY952yHbDs=", "qiWYVbHmrOWYqwuu3obkZvXkLBOYli24xtybgAfIKpo=", "hEQo7LMR1VQ5Jf4g0JW1LO/wB6YiPXZIdTsTVA40E90=", "wDF3W5imABbxLs7G1Cfu6uzlRwxHNOZxS72Uq9zzPQU=", "MGO3F13L8Z+N4KY9cnXYzBNfIpuZRPK1CkYGD+HMpys=", "U0gcwILI8ng0JSCo/zv05ZxXHq2kd6P1Mq5Q4fX0rr8=", "rRdpWlQJZHqmYtPpx16u6/ma4yFh8ibqBoOAOU2np5A=", "h4pM7OpgWbG9xqCXYZg1mdnY0aw1pT3v/7/8dSA7cMw=", "SixKvRxf3oKA1rZub4pFf2x6gP5Q+PQhFLNGkPCrFno=", "Nj+sR+G+5x3AOd40b79maVKshzLNN8iU1KGxrUgtns0=", "Wl3BEeUZHcDHiaz/HVgav0Bx3/3Xfm4MOVqzx39B3eY=", "d4h38hQ7eRX9MUvulmJ968nTrN9GMkQkyG3lfhggmuQ=", "yWcdlUwQJXKwgW1XAiZlkknP5vkXUfcIk3wvxdgguQA=", "pDxrGkdLPCDxb1BeBW+JAeJULJairLe2w6W4oWg3HKI=", "njo8M7YIFZq6c9LeW/hpH6oLjvKI2/ZNlZxNeGCPH6s=", "LKowJ52mYJ05Lge6veFrU4Yey/XmxABMqnYY1pbV254=", "7i4uUj0ka3OSEQc+5SQ/OIS/uxxCP3JQsYkSK7nt7l8=", "e8h5GrONq7RjU73o0X/gi1gWzAJNs1Ri6ScCcHcqYAg=", "2DF43KyNdi2y2Kc+HQaBlAnrWWS90XEI0Xi3bG0BThQ=", "lxclG1fwBoPbUw1rqPXvkNsJVsRLq9N9Xe7VrHyWPOU=", "zHkY3PbdoTkE6MUwrkg7ySnXBJU74pZrMFF14qanExs=", "EcrtnbEB9Hc0W0Vx67NNOTSM9taVdFa34KcdXSPVRfA=", "k4s6vD9m9wW5fOm6kLz5rtagNjv/ohaH/X5eRJiJZK8=", "CUtLz41qmtjRFw9TIg5WtXQpJBitj89XGHCnDEV4+eo=", "pbPVcF0lj8UmLgA9WnCWmjizinW9H+yJzfXk9nQehJ4=", "mJhonz38BFqNG+6UCCumSmt68NyOzvyi3VBg5u7X4tA=", "TI3qgwoL7n41Tuuf3EKnlfRznwMQkX8LR50CHjGqXs4=", "AxPmtVXLroXvmeArFYj334w0Ecp4I7rYz4FtI8rixZM=", "KCEo2h8ocfv7PNjLydiuOsXCzORaNwLnqHPLqW9rMO0=", "RMNGJtyccbkRIgTt8E4wkcT5ioHzqbcVqXVQIusvvRk=", "zf1JHaFe02KO/PrgIpWlqPaeEcN3SCCsmemrdV6KKWw=", "jwBe09a2+lug7Mi9fd+ogvbOWn7mmo9bCSYNBM80qm8=", "xEy5xz2iheO9/jPm/NmvyZehmdz+zSq/yfHFBjowcW4=", "AEf1FIWTw/heDkkNvWQodQuk9n1jjEERkabdnhtC8J8=", "mhUih/Va48baneComKRoSrMqeh5b32zeNLnqxVuD1LQ=", "KAnXdMpklyd6ilx42JeR++lSLFOoK2kowfk6JYUx17I=", "KQR1Y8DtuR0NUmxOfTG1IuJs4Plwhemog8VwFUFQLSE=", "qfKhgIsYG/LzZ5lW1xMrAVj1+z+qvV5IvtCic08qzSA=", "ph5Qwwbhjr45RYw1cOehjZ6clYZCloMjHA5vKyr4NqY=", "+YicNnxI/kNDIga9cFLeLlVgoATboLgkCSVD5Y7RzWY=", "Uf/OHoJoTt/8oFXLg8Rk5UP3xApHW4U7+nJNQigT7ro=", "4doWp05teernCtkGchVe9EZ5PlXMcxx6QLpNeWbdogs=", "3X2VB2lWcwHN9lxLoBjQL+8x/xPqPG+htcwQvKQtU/w=", "hSVJZnlyz8xf84LJCOmbVyr02qJDL5Gs/QGxuTTwK+U=", "saWLWUSz26hd2cHoz+V4cfLupghvY7G2FK/iwfpPDgc=", "haM+wuCoIdjyyo1AU7G2FA6uSAW3jxjoIQzbH+cS1qs=", "QNqeuL3mz5NkddzZ12Tx4tIl22yRa1giaoSEXOtJx7U=", "iTFwn0X9pC4d/z4oxG5bG7q1Wq9IzuRGRsD6eqbbPI4=", "tZoP9I3x0lpEOAAnVlwOghDqvp5WrWdQtVtlBbGmF1c=", "PYiXgFlAW+s0nJcFScGIEboJGZWwKZED4NPABJkBNIo=", "abYsbTPqJm978GzdEo+qbLCg++P4vMAVbf6XZkgjquA=", "L+oMpB28yFeQ5zya2Vrnr4TCv4TMYrEylnw8Byi4JsI=", "verjGe9uBzaa+pNQxeG3zuFP/zHKNLKqFfsKQ56Zer0=", "w2niZcN2dW1l5yMIwXOE6CLyKfYDw/s+F5rlhMqmgPg=", "6unFwZghquWoEQGH70+FDYwo0NaW/8cW6WNoYLvJFsk=", "X/gT+tTwWpZaBhDoe2hNkFN48Jy1fTqt10MiJp9C4Dc=", "X7URyFubuVTh5Ey9F3IUolINYRj1S6a+HfLpCu/fhFM=", "65HwwwZGIBZPwojour/lBDUOmpAPCjP3fDwYzB/c20A=", "AU2LCMMr08qArU0hgwp+gwkbgUGi/AzvntYPFwEC9jU=", "uHgH5NIlo+FOnCHOd06CV7IPh2p/HVJfcAt1/ezF9lY=", "Mckc7H4Ldke6zSKggyD8FlfaV6mhiuUCvwu/lFNic6M=", "xOTfTxPA/h9mJvgjrJNIZE3BFK4+wAUkCy4Irfrga0k=", "cfGX43H7xAUP8phvbgKgUZDxSvnwaHDsk8Cs/gG/zak="], "CachedAssets": {"SLNT8cloWz2hO830AafiLJz5zuKbnKyoOLdWdE269Oo=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\css\\site.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ckzg008lq5", "Integrity": "9CcCYh0eTd6yYsWH8L13KslPxFsRf/tfLYv2tQowxH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 1088, "LastWriteTime": "2025-07-10T09:32:42.3721626+00:00"}, "0CzU3v7wyBoivkT6eon7W5wN/R/N+9LZUIMjS+8eupE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\favicon.ico", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-10T09:02:16.6170813+00:00"}, "rs7nkmKf3819EnyxnTr4Wc4BP2X2HIyPuy86DjTWoE8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\js\\site.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-10T09:02:16.3161622+00:00"}, "QT6tbgRF7MfuWLlGzOR+1nsOuaZ1mpHdLIFQnRxMBes=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-07-10T09:02:16.5041448+00:00"}, "vVlAWhVcfxZJR+taj3raiiV7wekkAkMCaj9YLDXFSFM=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-07-10T09:02:16.5069592+00:00"}, "KCIs7LbJyHWGAykpydXYGO1iUoGrIbPy2pY952yHbDs=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-07-10T09:02:16.5083246+00:00"}, "qiWYVbHmrOWYqwuu3obkZvXkLBOYli24xtybgAfIKpo=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-07-10T09:02:16.5094135+00:00"}, "hEQo7LMR1VQ5Jf4g0JW1LO/wB6YiPXZIdTsTVA40E90=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-07-10T09:02:16.5105851+00:00"}, "wDF3W5imABbxLs7G1Cfu6uzlRwxHNOZxS72Uq9zzPQU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-07-10T09:02:16.5122016+00:00"}, "MGO3F13L8Z+N4KY9cnXYzBNfIpuZRPK1CkYGD+HMpys=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-07-10T09:02:16.5144917+00:00"}, "U0gcwILI8ng0JSCo/zv05ZxXHq2kd6P1Mq5Q4fX0rr8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-07-10T09:02:16.5161388+00:00"}, "rRdpWlQJZHqmYtPpx16u6/ma4yFh8ibqBoOAOU2np5A=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-07-10T09:02:16.5171559+00:00"}, "h4pM7OpgWbG9xqCXYZg1mdnY0aw1pT3v/7/8dSA7cMw=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-07-10T09:02:16.5188061+00:00"}, "SixKvRxf3oKA1rZub4pFf2x6gP5Q+PQhFLNGkPCrFno=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-07-10T09:02:16.5198309+00:00"}, "Nj+sR+G+5x3AOd40b79maVKshzLNN8iU1KGxrUgtns0=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-07-10T09:02:16.5213204+00:00"}, "Wl3BEeUZHcDHiaz/HVgav0Bx3/3Xfm4MOVqzx39B3eY=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-07-10T09:02:16.5223207+00:00"}, "d4h38hQ7eRX9MUvulmJ968nTrN9GMkQkyG3lfhggmuQ=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-07-10T09:02:16.5239383+00:00"}, "yWcdlUwQJXKwgW1XAiZlkknP5vkXUfcIk3wvxdgguQA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-07-10T09:02:16.5259496+00:00"}, "pDxrGkdLPCDxb1BeBW+JAeJULJairLe2w6W4oWg3HKI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-07-10T09:02:16.5274391+00:00"}, "njo8M7YIFZq6c9LeW/hpH6oLjvKI2/ZNlZxNeGCPH6s=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-07-10T09:02:16.528027+00:00"}, "LKowJ52mYJ05Lge6veFrU4Yey/XmxABMqnYY1pbV254=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-07-10T09:02:16.5314645+00:00"}, "7i4uUj0ka3OSEQc+5SQ/OIS/uxxCP3JQsYkSK7nt7l8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-07-10T09:02:16.533514+00:00"}, "e8h5GrONq7RjU73o0X/gi1gWzAJNs1Ri6ScCcHcqYAg=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-07-10T09:02:16.5348205+00:00"}, "2DF43KyNdi2y2Kc+HQaBlAnrWWS90XEI0Xi3bG0BThQ=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-07-10T09:02:16.5362358+00:00"}, "lxclG1fwBoPbUw1rqPXvkNsJVsRLq9N9Xe7VrHyWPOU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-07-10T09:02:16.5384197+00:00"}, "zHkY3PbdoTkE6MUwrkg7ySnXBJU74pZrMFF14qanExs=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-07-10T09:02:16.5400484+00:00"}, "EcrtnbEB9Hc0W0Vx67NNOTSM9taVdFa34KcdXSPVRfA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-07-10T09:02:16.5410522+00:00"}, "k4s6vD9m9wW5fOm6kLz5rtagNjv/ohaH/X5eRJiJZK8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-07-10T09:02:16.5423442+00:00"}, "CUtLz41qmtjRFw9TIg5WtXQpJBitj89XGHCnDEV4+eo=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-07-10T09:02:16.5471238+00:00"}, "pbPVcF0lj8UmLgA9WnCWmjizinW9H+yJzfXk9nQehJ4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-07-10T09:02:16.5486622+00:00"}, "mJhonz38BFqNG+6UCCumSmt68NyOzvyi3VBg5u7X4tA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-07-10T09:02:16.5513837+00:00"}, "TI3qgwoL7n41Tuuf3EKnlfRznwMQkX8LR50CHjGqXs4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-07-10T09:02:16.5523831+00:00"}, "AxPmtVXLroXvmeArFYj334w0Ecp4I7rYz4FtI8rixZM=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-07-10T09:02:16.5569978+00:00"}, "KCEo2h8ocfv7PNjLydiuOsXCzORaNwLnqHPLqW9rMO0=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-07-10T09:02:16.5595221+00:00"}, "RMNGJtyccbkRIgTt8E4wkcT5ioHzqbcVqXVQIusvvRk=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-07-10T09:02:16.5638255+00:00"}, "zf1JHaFe02KO/PrgIpWlqPaeEcN3SCCsmemrdV6KKWw=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-07-10T09:02:16.566822+00:00"}, "jwBe09a2+lug7Mi9fd+ogvbOWn7mmo9bCSYNBM80qm8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-07-10T09:02:16.5709379+00:00"}, "xEy5xz2iheO9/jPm/NmvyZehmdz+zSq/yfHFBjowcW4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-07-10T09:02:16.5729579+00:00"}, "AEf1FIWTw/heDkkNvWQodQuk9n1jjEERkabdnhtC8J8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-07-10T09:02:16.5760787+00:00"}, "mhUih/Va48baneComKRoSrMqeh5b32zeNLnqxVuD1LQ=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-07-10T09:02:16.5787182+00:00"}, "KAnXdMpklyd6ilx42JeR++lSLFOoK2kowfk6JYUx17I=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-07-10T09:02:16.5822608+00:00"}, "KQR1Y8DtuR0NUmxOfTG1IuJs4Plwhemog8VwFUFQLSE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-07-10T09:02:16.5835365+00:00"}, "qfKhgIsYG/LzZ5lW1xMrAVj1+z+qvV5IvtCic08qzSA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-07-10T09:02:16.5866593+00:00"}, "ph5Qwwbhjr45RYw1cOehjZ6clYZCloMjHA5vKyr4NqY=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-07-10T09:02:16.5892064+00:00"}, "+YicNnxI/kNDIga9cFLeLlVgoATboLgkCSVD5Y7RzWY=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-07-10T09:02:16.5917149+00:00"}, "Uf/OHoJoTt/8oFXLg8Rk5UP3xApHW4U7+nJNQigT7ro=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-07-10T09:02:16.5927295+00:00"}, "4doWp05teernCtkGchVe9EZ5PlXMcxx6QLpNeWbdogs=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-07-10T09:02:16.5956661+00:00"}, "3X2VB2lWcwHN9lxLoBjQL+8x/xPqPG+htcwQvKQtU/w=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-10T09:02:16.61282+00:00"}, "hSVJZnlyz8xf84LJCOmbVyr02qJDL5Gs/QGxuTTwK+U=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-10T09:02:16.6216241+00:00"}, "saWLWUSz26hd2cHoz+V4cfLupghvY7G2FK/iwfpPDgc=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-10T09:02:16.6229703+00:00"}, "haM+wuCoIdjyyo1AU7G2FA6uSAW3jxjoIQzbH+cS1qs=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-10T09:02:16.6240771+00:00"}, "QNqeuL3mz5NkddzZ12Tx4tIl22yRa1giaoSEXOtJx7U=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-07-10T09:02:16.3331304+00:00"}, "iTFwn0X9pC4d/z4oxG5bG7q1Wq9IzuRGRsD6eqbbPI4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-07-10T09:02:16.3352583+00:00"}, "tZoP9I3x0lpEOAAnVlwOghDqvp5WrWdQtVtlBbGmF1c=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-07-10T09:02:16.3372759+00:00"}, "PYiXgFlAW+s0nJcFScGIEboJGZWwKZED4NPABJkBNIo=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-07-10T09:02:16.3382998+00:00"}, "abYsbTPqJm978GzdEo+qbLCg++P4vMAVbf6XZkgjquA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-10T09:02:16.6186085+00:00"}, "W18W+air8HY2js46gQnZcix7gQ98kqjzCNhP+R6Ey34=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\js\\app\\main.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "js/app/main#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ym4ndc21gc", "Integrity": "OrS5eb1LejRsnSEZagyZ24p/o3F/vYRpgPmKrsUx3B0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app\\main.js", "FileLength": 1112, "LastWriteTime": "2025-07-10T09:22:48.0179941+00:00"}, "dy5CE6pA0NsxyJDi79Vpy1X8grIaELFwJY5S1UKWC1Y=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\js\\app\\utils.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "js/app/utils#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6o8fd5dz0e", "Integrity": "WjUUsiWztw/C6VxF5fsat3ff4Q2sFB5glnhGFgwaEDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app\\utils.js", "FileLength": 1473, "LastWriteTime": "2025-07-10T09:23:00.1469813+00:00"}, "0wetNwxFEOz1r1n9z5+2nOOJKDXyqLxo0l3CSx9CPiU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\js\\vendor\\custom-plugin.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "js/vendor/custom-plugin#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qt787s6x86", "Integrity": "ntCj2To6WopJZ9FNRCeXRtYmmdLCbl2heTOzUYoZyVQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\vendor\\custom-plugin.js", "FileLength": 2106, "LastWriteTime": "2025-07-10T09:23:12.3166378+00:00"}, "2iMBoaQzl5BWqaX+jFjnR0YwpMlmfIrg4vz6V22QS0k=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\js\\vendor\\analytics.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "js/vendor/analytics#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vk2zyi5bem", "Integrity": "MQ5CSDVbcOTIiuVb9caVuDj/iJ8FEOMws3EdkgB7Dn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\vendor\\analytics.js", "FileLength": 1941, "LastWriteTime": "2025-07-10T09:23:24.0814501+00:00"}, "gEmsboixz3k7bOvmERsXYmQku2PJIkyo6zIxo6oqvHU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\js\\vendor\\jquery.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "js/vendor/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\vendor\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-10T09:23:45.3569297+00:00"}, "WUA6KKQuqLTOwef8JxdcKsu7tzujKRwnnJz2a0Fi8Ig=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\js\\dist\\app.bundle.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "js/dist/app.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5qtqrbhd5u", "Integrity": "/W9SJ92tZrgEjpyhUu5SZmJS43a8+Ztz4NlZP1UXJEY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dist\\app.bundle.js", "FileLength": 906, "LastWriteTime": "2025-07-10T09:42:20.2164137+00:00"}, "qZCT+WYBhhnFfDHICbUFZ8kKGXjr1wJqVBh2NeFoMfg=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\js\\dist\\vendor.bundle.js", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "js/dist/vendor.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ijvy27a3wj", "Integrity": "0g0c1esdrWztWKuGk2aWj0IODu1BE02RybCBm70iJpk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dist\\vendor.bundle.js", "FileLength": 87565, "LastWriteTime": "2025-07-10T09:42:20.2191375+00:00"}, "L+oMpB28yFeQ5zya2Vrnr4TCv4TMYrEylnw8Byi4JsI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\test.html", "SourceId": "GulpBundle", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\BundleStaticFiles\\Debugging\\GulpBundle\\GulpBundle\\wwwroot\\", "BasePath": "_content/GulpBundle", "RelativePath": "test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m3qdd4fd19", "Integrity": "nk3OrZ5LYX/5Fwea6ODjAG6MJYk57iuYmnWjU758fC8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test.html", "FileLength": 6427, "LastWriteTime": "2025-07-10T09:40:29.6105474+00:00"}}, "CachedCopyCandidates": {}}