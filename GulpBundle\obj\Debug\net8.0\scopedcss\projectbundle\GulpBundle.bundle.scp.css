/* _content/GulpBundle/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-pa7qw46dpf] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-pa7qw46dpf] {
  color: #0077cc;
}

.btn-primary[b-pa7qw46dpf] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-pa7qw46dpf], .nav-pills .show > .nav-link[b-pa7qw46dpf] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-pa7qw46dpf] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-pa7qw46dpf] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-pa7qw46dpf] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-pa7qw46dpf] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-pa7qw46dpf] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
