using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace GulpBundle.Helpers
{
    public static class BundleHelper
    {
        /// <summary>
        /// Renders script tags for vendor JavaScript files.
        /// In development mode, includes individual source files with source maps.
        /// In production mode, includes only the minified bundle.
        /// </summary>
        public static IHtmlContent RenderVendorScripts(this IHtmlHelper htmlHelper, IWebHostEnvironment env)
        {
            var scripts = new List<string>();

            if (env.IsDevelopment())
            {
                // Development mode - include individual source files for debugging
                scripts.Add("/js/vendor/jquery.js");
                scripts.Add("/js/vendor/custom-plugin.js");
                scripts.Add("/js/vendor/analytics.js");
            }
            else
            {
                // Production mode - include only the minified bundle
                scripts.Add("/js/dist/vendor.bundle.js");
            }

            return GenerateScriptTags(scripts);
        }

        /// <summary>
        /// Renders script tags for application JavaScript files.
        /// In development mode, includes individual source files with source maps.
        /// In production mode, includes only the minified bundle.
        /// </summary>
        public static IHtmlContent RenderAppScripts(this IHtmlHelper htmlHelper, IWebHostEnvironment env)
        {
            var scripts = new List<string>();

            if (env.IsDevelopment())
            {
                // Development mode - include individual source files for debugging
                scripts.Add("/js/app/utils.js");
                scripts.Add("/js/app/main.js");
            }
            else
            {
                // Production mode - include only the minified bundle
                scripts.Add("/js/dist/app.bundle.js");
            }

            return GenerateScriptTags(scripts);
        }

        private static IHtmlContent GenerateScriptTags(List<string> scripts)
        {
            var scriptTags = scripts.Select(script => $"<script src=\"{script}\"></script>");
            return new HtmlString(string.Join(Environment.NewLine, scriptTags));
        }
    }
}
