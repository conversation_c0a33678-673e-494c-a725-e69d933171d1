((e,t)=>{"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(e.document)return t(e);throw new Error("jQuery requires a window with a document")}:t(e)})("undefined"!=typeof window?window:this,function(w,M){function m(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item}function R(e){return null!=e&&e===e.window}var t=[],I=Object.getPrototypeOf,s=t.slice,F=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},W=t.push,x=t.indexOf,$={},B=$.toString,_=$.hasOwnProperty,U=_.toString,z=U.call(Object),g={},T=w.document,X={type:!0,src:!0,nonce:!0,noModule:!0};function V(e,t,n){var r,i,o=(n=n||T).createElement("script");if(o.text=e,t)for(r in X)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function G(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?$[B.call(e)]||"object":typeof e}var e="3.7.1",Y=/HTML$/i,C=function(e,t){return new C.fn.init(e,t)};function Q(e){var t=!!e&&"length"in e&&e.length,n=G(e);return!m(e)&&!R(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}function b(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}C.fn=C.prototype={jquery:e,constructor:C,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=C.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return C.each(this,e)},map:function(n){return this.pushStack(C.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(C.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(C.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:W,sort:t.sort,splice:t.splice},C.extend=C.fn.extend=function(){var e,t,n,r,i,o=arguments[0]||{},a=1,s=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[a]||{},a++),"object"==typeof o||m(o)||(o={}),a===s&&(o=this,a--);a<s;a++)if(null!=(e=arguments[a]))for(t in e)n=e[t],"__proto__"!==t&&o!==n&&(u&&n&&(C.isPlainObject(n)||(r=Array.isArray(n)))?(i=o[t],i=r&&!Array.isArray(i)?[]:r||C.isPlainObject(i)?i:{},r=!1,o[t]=C.extend(u,i,n)):void 0!==n&&(o[t]=n));return o},C.extend({expando:"jQuery"+(e+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==B.call(e)||(e=I(e))&&("function"!=typeof(e=_.call(e,"constructor")&&e.constructor)||U.call(e)!==z))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){V(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(Q(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)for(;t=e[r++];)n+=C.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){t=t||[];return null!=e&&(Q(Object(e))?C.merge(t,"string"==typeof e?[e]:e):W.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:x.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,e=e&&(e.ownerDocument||e).documentElement;return!Y.test(t||e&&e.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!=a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(Q(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return F(a)},guid:1,support:g}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=t[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){$["[object "+t+"]"]=t.toLowerCase()});var J=t.pop,K=t.sort,Z=t.splice,n="[\\x20\\t\\r\\n\\f]",ee=new RegExp("^"+n+"+|((?:^|[^\\\\])(?:\\\\.)*)"+n+"+$","g"),te=(C.contains=function(e,t){t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(e.contains?e.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))},/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g);function ne(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}C.escapeSelector=function(e){return(e+"").replace(te,ne)};var re,k,ie,oe,ae,E,r,S,p,se,i=T,ue=W,j=ue,A=C.expando,D=0,le=0,ce=De(),fe=De(),pe=De(),de=De(),he=function(e,t){return e===t&&(ae=!0),0},ge="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",e="(?:\\\\[\\da-fA-F]{1,6}"+n+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",o="\\["+n+"*("+e+")(?:"+n+"*([*^$|!~]?=)"+n+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+e+"))|)"+n+"*\\]",a=":("+e+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+o+")*)|.*)\\)|)",ye=new RegExp(n+"+","g"),ve=new RegExp("^"+n+"*,"+n+"*"),me=new RegExp("^"+n+"*([>+~]|"+n+")"+n+"*"),xe=new RegExp(n+"|>"),be=new RegExp(a),we=new RegExp("^"+e+"$"),Te={ID:new RegExp("^#("+e+")"),CLASS:new RegExp("^\\.("+e+")"),TAG:new RegExp("^("+e+"|[*])"),ATTR:new RegExp("^"+o),PSEUDO:new RegExp("^"+a),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+n+"*(even|odd|(([+-]|)(\\d*)n|)"+n+"*(?:([+-]|)"+n+"*(\\d+)|))"+n+"*\\)|)","i"),bool:new RegExp("^(?:"+ge+")$","i"),needsContext:new RegExp("^"+n+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+n+"*((?:-\\d)?\\d*)"+n+"*\\)|)(?=[^-]|$)","i")},Ce=/^(?:input|select|textarea|button)$/i,ke=/^h\d$/i,Ee=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Se=/[+~]/,f=new RegExp("\\\\[\\da-fA-F]{1,6}"+n+"?|\\\\([^\\r\\n\\f])","g"),d=function(e,t){e="0x"+e.slice(1)-65536;return t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))},je=function(){Oe()},Ae=Ie(function(e){return!0===e.disabled&&b(e,"fieldset")},{dir:"parentNode",next:"legend"});try{j.apply(t=s.call(i.childNodes),i.childNodes),t[i.childNodes.length].nodeType}catch(e){j={apply:function(e,t){ue.apply(e,s.call(t))},call:function(e){ue.apply(e,s.call(arguments,1))}}}function N(t,e,n,r){var i,o,a,s,u,l,c=e&&e.ownerDocument,f=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==f&&9!==f&&11!==f)return n;if(!r&&(Oe(e),e=e||E,S)){if(11!==f&&(s=Ee.exec(t)))if(i=s[1]){if(9===f){if(!(l=e.getElementById(i)))return n;if(l.id===i)return j.call(n,l),n}else if(c&&(l=c.getElementById(i))&&N.contains(e,l)&&l.id===i)return j.call(n,l),n}else{if(s[2])return j.apply(n,e.getElementsByTagName(t)),n;if((i=s[3])&&e.getElementsByClassName)return j.apply(n,e.getElementsByClassName(i)),n}if(!(de[t+" "]||p&&p.test(t))){if(l=t,c=e,1===f&&(xe.test(t)||me.test(t))){for((c=Se.test(t)&&He(e.parentNode)||e)==e&&g.scope||((a=e.getAttribute("id"))?a=C.escapeSelector(a):e.setAttribute("id",a=A)),o=(u=Me(t)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+Re(u[o]);l=u.join(",")}try{return j.apply(n,c.querySelectorAll(l)),n}catch(e){de(t,!0)}finally{a===A&&e.removeAttribute("id")}}}return Ue(t.replace(ee,"$1"),e,n,r)}function De(){var n=[];function r(e,t){return n.push(e+" ")>k.cacheLength&&delete r[n.shift()],r[e+" "]=t}return r}function u(e){return e[A]=!0,e}function Ne(e){var t=E.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function qe(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&Ae(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function Le(a){return u(function(o){return o=+o,u(function(e,t){for(var n,r=a([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function He(e){return e&&void 0!==e.getElementsByTagName&&e}function Oe(e){var e=e?e.ownerDocument||e:i;return e!=E&&9===e.nodeType&&e.documentElement&&(r=(E=e).documentElement,S=!C.isXMLDoc(E),se=r.matches||r.webkitMatchesSelector||r.msMatchesSelector,r.msMatchesSelector&&i!=E&&(e=E.defaultView)&&e.top!==e&&e.addEventListener("unload",je),g.getById=Ne(function(e){return r.appendChild(e).id=C.expando,!E.getElementsByName||!E.getElementsByName(C.expando).length}),g.disconnectedMatch=Ne(function(e){return se.call(e,"*")}),g.scope=Ne(function(){return E.querySelectorAll(":scope")}),g.cssHas=Ne(function(){try{E.querySelector(":has(*,:jqfake)")}catch(e){return 1}}),g.getById?(k.filter.ID=function(e){var t=e.replace(f,d);return function(e){return e.getAttribute("id")===t}},k.find.ID=function(e,t){if(void 0!==t.getElementById&&S)return(t=t.getElementById(e))?[t]:[]}):(k.filter.ID=function(e){var t=e.replace(f,d);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},k.find.ID=function(e,t){if(void 0!==t.getElementById&&S){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),k.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},k.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&S)return t.getElementsByClassName(e)},p=[],Ne(function(e){var t;r.appendChild(e).innerHTML="<a id='"+A+"' href='' disabled='disabled'></a><select id='"+A+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||p.push("\\["+n+"*(?:value|"+ge+")"),e.querySelectorAll("[id~="+A+"-]").length||p.push("~="),e.querySelectorAll("a#"+A+"+*").length||p.push(".#.+[+~]"),e.querySelectorAll(":checked").length||p.push(":checked"),(t=E.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),r.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),(t=E.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||p.push("\\["+n+"*name"+n+"*="+n+"*(?:''|\"\")")}),g.cssHas||p.push(":has"),p=p.length&&new RegExp(p.join("|")),he=function(e,t){var n;return e===t?(ae=!0,0):(n=!e.compareDocumentPosition-!t.compareDocumentPosition)||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!g.sortDetached&&t.compareDocumentPosition(e)===n?e===E||e.ownerDocument==i&&N.contains(i,e)?-1:t===E||t.ownerDocument==i&&N.contains(i,t)?1:oe?x.call(oe,e)-x.call(oe,t):0:4&n?-1:1)}),E}for(re in N.matches=function(e,t){return N(e,null,null,t)},N.matchesSelector=function(e,t){if(Oe(e),S&&!de[t+" "]&&(!p||!p.test(t)))try{var n=se.call(e,t);if(n||g.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){de(t,!0)}return 0<N(t,E,null,[e]).length},N.contains=function(e,t){return(e.ownerDocument||e)!=E&&Oe(e),C.contains(e,t)},N.attr=function(e,t){(e.ownerDocument||e)!=E&&Oe(e);var n=k.attrHandle[t.toLowerCase()],n=n&&_.call(k.attrHandle,t.toLowerCase())?n(e,t,!S):void 0;return void 0!==n?n:e.getAttribute(t)},N.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},C.uniqueSort=function(e){var t,n=[],r=0,i=0;if(ae=!g.sortStable,oe=!g.sortStable&&s.call(e,0),K.call(e,he),ae){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)Z.call(e,n[r],1)}return oe=null,e},C.fn.uniqueSort=function(){return this.pushStack(C.uniqueSort(s.apply(this)))},(k=C.expr={cacheLength:50,createPseudo:u,match:Te,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(f,d),e[3]=(e[3]||e[4]||e[5]||"").replace(f,d),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||N.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&N.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Te.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&be.test(n)&&(t=(t=Me(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(f,d).toLowerCase();return"*"===e?function(){return!0}:function(e){return b(e,t)}},CLASS:function(e){var t=ce[e+" "];return t||(t=new RegExp("(^|"+n+")"+e+"("+n+"|$)"))&&ce(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=N.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace(ye," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(d,e,t,h,g){var y="nth"!==d.slice(0,3),v="last"!==d.slice(-4),m="of-type"===e;return 1===h&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u=y!=v?"nextSibling":"previousSibling",l=e.parentNode,c=m&&e.nodeName.toLowerCase(),f=!n&&!m,p=!1;if(l){if(y){for(;u;){for(o=e;o=o[u];)if(m?b(o,c):1===o.nodeType)return!1;s=u="only"===d&&!s&&"nextSibling"}return!0}if(s=[v?l.firstChild:l.lastChild],v&&f){for(p=(a=(r=(i=l[A]||(l[A]={}))[d]||[])[0]===D&&r[1])&&r[2],o=a&&l.childNodes[a];o=++a&&o&&o[u]||(p=a=0,s.pop());)if(1===o.nodeType&&++p&&o===e){i[d]=[D,a,p];break}}else if(!1===(p=f?a=(r=(i=e[A]||(e[A]={}))[d]||[])[0]===D&&r[1]:p))for(;(o=++a&&o&&o[u]||(p=a=0,s.pop()))&&((m?!b(o,c):1!==o.nodeType)||!++p||(f&&((i=o[A]||(o[A]={}))[d]=[D,p]),o!==e)););return(p-=g)===h||p%h==0&&0<=p/h}}},PSEUDO:function(e,o){var t,a=k.pseudos[e]||k.setFilters[e.toLowerCase()]||N.error("unsupported pseudo: "+e);return a[A]?a(o):1<a.length?(t=[e,e,"",o],k.setFilters.hasOwnProperty(e.toLowerCase())?u(function(e,t){for(var n,r=a(e,o),i=r.length;i--;)e[n=x.call(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:u(function(e){var r=[],i=[],s=_e(e.replace(ee,"$1"));return s[A]?u(function(e,t,n,r){for(var i,o=s(e,null,r,[]),a=e.length;a--;)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:u(function(t){return function(e){return 0<N(t,e).length}}),contains:u(function(t){return t=t.replace(f,d),function(e){return-1<(e.textContent||C.text(e)).indexOf(t)}}),lang:u(function(n){return we.test(n||"")||N.error("unsupported lang: "+n),n=n.replace(f,d).toLowerCase(),function(e){var t;do{if(t=S?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=w.location&&w.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===(()=>{try{return E.activeElement}catch(e){}})()&&E.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:qe(!1),disabled:qe(!0),checked:function(e){return b(e,"input")&&!!e.checked||b(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!k.pseudos.empty(e)},header:function(e){return ke.test(e.nodeName)},input:function(e){return Ce.test(e.nodeName)},button:function(e){return b(e,"input")&&"button"===e.type||b(e,"button")},text:function(e){return b(e,"input")&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:Le(function(){return[0]}),last:Le(function(e,t){return[t-1]}),eq:Le(function(e,t,n){return[n<0?n+t:n]}),even:Le(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:Le(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:Le(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:Le(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=k.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})k.pseudos[re]=(t=>function(e){return b(e,"input")&&e.type===t})(re);for(re in{submit:!0,reset:!0})k.pseudos[re]=(t=>function(e){return(b(e,"input")||b(e,"button"))&&e.type===t})(re);function Pe(){}function Me(e,t){var n,r,i,o,a,s,u,l=fe[e+" "];if(l)return t?0:l.slice(0);for(a=e,s=[],u=k.preFilter;a;){for(o in n&&!(r=ve.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=me.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(ee," ")}),a=a.slice(n.length)),k.filter)!(r=Te[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?N.error(e):fe(e,s).slice(0)}function Re(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function Ie(a,e,t){var s=e.dir,u=e.next,l=u||s,c=t&&"parentNode"===l,f=le++;return e.first?function(e,t,n){for(;e=e[s];)if(1===e.nodeType||c)return a(e,t,n);return!1}:function(e,t,n){var r,i,o=[D,f];if(n){for(;e=e[s];)if((1===e.nodeType||c)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||c)if(i=e[A]||(e[A]={}),u&&b(e,u))e=e[s]||e;else{if((r=i[l])&&r[0]===D&&r[1]===f)return o[2]=r[2];if((i[l]=o)[2]=a(e,t,n))return!0}return!1}}function Fe(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function We(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)!(o=e[s])||n&&!n(o,r,i)||(a.push(o),l&&t.push(s));return a}function $e(d,h,g,y,v,e){return y&&!y[A]&&(y=$e(y)),v&&!v[A]&&(v=$e(v,e)),u(function(e,t,n,r){var i,o,a,s,u=[],l=[],c=t.length,f=e||((e,t,n)=>{for(var r=0,i=t.length;r<i;r++)N(e,t[r],n);return n})(h||"*",n.nodeType?[n]:n,[]),p=!d||!e&&h?f:We(f,u,d,n,r);if(g?g(p,s=v||(e?d:c||y)?[]:t,n,r):s=p,y)for(i=We(s,l),y(i,[],n,r),o=i.length;o--;)(a=i[o])&&(s[l[o]]=!(p[l[o]]=a));if(e){if(v||d){if(v){for(i=[],o=s.length;o--;)(a=s[o])&&i.push(p[o]=a);v(null,s=[],i,r)}for(o=s.length;o--;)(a=s[o])&&-1<(i=v?x.call(e,a):u[o])&&(e[i]=!(t[i]=a))}}else s=We(s===t?s.splice(c,s.length):s),v?v(null,t,s,r):j.apply(t,s)})}function Be(y,v){function e(e,t,n,r,i){var o,a,s,u=0,l="0",c=e&&[],f=[],p=ie,d=e||x&&k.find.TAG("*",i),h=D+=null==p?1:Math.random()||.1,g=d.length;for(i&&(ie=t==E||t||i);l!==g&&null!=(o=d[l]);l++){if(x&&o){for(a=0,t||o.ownerDocument==E||(Oe(o),n=!S);s=y[a++];)if(s(o,t||E,n)){j.call(r,o);break}i&&(D=h)}m&&((o=!s&&o)&&u--,e)&&c.push(o)}if(u+=l,m&&l!==u){for(a=0;s=v[a++];)s(c,f,t,n);if(e){if(0<u)for(;l--;)c[l]||f[l]||(f[l]=J.call(r));f=We(f)}j.apply(r,f),i&&!e&&0<f.length&&1<u+v.length&&C.uniqueSort(r)}return i&&(D=h,ie=p),c}var m=0<v.length,x=0<y.length;return m?u(e):e}function _e(e,t){var n,r=[],i=[],o=pe[e+" "];if(!o){for(n=(t=t||Me(e)).length;n--;)((o=function e(t){for(var r,n,i,o=t.length,a=k.relative[t[0].type],s=a||k.relative[" "],u=a?1:0,l=Ie(function(e){return e===r},s,!0),c=Ie(function(e){return-1<x.call(r,e)},s,!0),f=[function(e,t,n){return e=!a&&(n||t!=ie)||((r=t).nodeType?l:c)(e,t,n),r=null,e}];u<o;u++)if(n=k.relative[t[u].type])f=[Ie(Fe(f),n)];else{if((n=k.filter[t[u].type].apply(null,t[u].matches))[A]){for(i=++u;i<o&&!k.relative[t[i].type];i++);return $e(1<u&&Fe(f),1<u&&Re(t.slice(0,u-1).concat({value:" "===t[u-2].type?"*":""})).replace(ee,"$1"),n,u<i&&e(t.slice(u,i)),i<o&&e(t=t.slice(i)),i<o&&Re(t))}f.push(n)}return Fe(f)}(t[n]))[A]?r:i).push(o);(o=pe(e,Be(i,r))).selector=e}return o}function Ue(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&Me(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&S&&k.relative[o[1].type]){if(!(t=(k.find.ID(a.matches[0].replace(f,d),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=Te.needsContext.test(e)?0:o.length;i--&&(a=o[i],!k.relative[s=a.type]);)if((u=k.find[s])&&(r=u(a.matches[0].replace(f,d),Se.test(o[0].type)&&He(t.parentNode)||t))){if(o.splice(i,1),e=r.length&&Re(o))break;return j.apply(n,r),n}}return(l||_e(e,c))(r,t,!S,n,!t||Se.test(e)&&He(t.parentNode)||t),n}Pe.prototype=k.filters=k.pseudos,k.setFilters=new Pe,g.sortStable=A.split("").sort(he).join("")===A,Oe(),g.sortDetached=Ne(function(e){return 1&e.compareDocumentPosition(E.createElement("fieldset"))}),C.find=N,C.expr[":"]=C.expr.pseudos,C.unique=C.uniqueSort,N.compile=_e,N.select=Ue,N.setDocument=Oe,N.tokenize=Me,N.escape=C.escapeSelector,N.getText=C.text,N.isXML=C.isXMLDoc,N.selectors=C.expr,N.support=C.support,N.uniqueSort=C.uniqueSort;function ze(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&C(e).is(n))break;r.push(e)}return r}function Xe(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var Ve=C.expr.match.needsContext,Ge=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Ye(e,n,r){return m(n)?C.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?C.grep(e,function(e){return e===n!==r}):"string"!=typeof n?C.grep(e,function(e){return-1<x.call(n,e)!==r}):C.filter(n,e,r)}C.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?C.find.matchesSelector(r,e)?[r]:[]:C.find.matches(e,C.grep(t,function(e){return 1===e.nodeType}))},C.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(C(e).filter(function(){for(t=0;t<r;t++)if(C.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)C.find(e,i[t],n);return 1<r?C.uniqueSort(n):n},filter:function(e){return this.pushStack(Ye(this,e||[],!1))},not:function(e){return this.pushStack(Ye(this,e||[],!0))},is:function(e){return!!Ye(this,"string"==typeof e&&Ve.test(e)?C(e):e||[],!1).length}});var Qe,Je=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Ke=((C.fn.init=function(e,t,n){if(e){if(n=n||Qe,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):m(e)?void 0!==n.ready?n.ready(e):e(C):C.makeArray(e,this);if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:Je.exec(e))||!r[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(r[1]){if(t=t instanceof C?t[0]:t,C.merge(this,C.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:T,!0)),Ge.test(r[1])&&C.isPlainObject(t))for(var r in t)m(this[r])?this[r](t[r]):this.attr(r,t[r])}else(n=T.getElementById(r[2]))&&(this[0]=n,this.length=1)}return this}).prototype=C.fn,Qe=C(T),/^(?:parents|prev(?:Until|All))/),Ze={children:!0,contents:!0,next:!0,prev:!0};function et(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}C.fn.extend({has:function(e){var t=C(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(C.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&C(e);if(!Ve.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&C.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?C.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?x.call(C(e),this[0]):x.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),C.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return ze(e,"parentNode")},parentsUntil:function(e,t,n){return ze(e,"parentNode",n)},next:function(e){return et(e,"nextSibling")},prev:function(e){return et(e,"previousSibling")},nextAll:function(e){return ze(e,"nextSibling")},prevAll:function(e){return ze(e,"previousSibling")},nextUntil:function(e,t,n){return ze(e,"nextSibling",n)},prevUntil:function(e,t,n){return ze(e,"previousSibling",n)},siblings:function(e){return Xe((e.parentNode||{}).firstChild,e)},children:function(e){return Xe(e.firstChild)},contents:function(e){return null!=e.contentDocument&&I(e.contentDocument)?e.contentDocument:(b(e,"template")&&(e=e.content||e),C.merge([],e.childNodes))}},function(r,i){C.fn[r]=function(e,t){var n=C.map(this,i,e);return(t="Until"!==r.slice(-5)?e:t)&&"string"==typeof t&&(n=C.filter(t,n)),1<this.length&&(Ze[r]||C.uniqueSort(n),Ke.test(r))&&n.reverse(),this.pushStack(n)}});var q=/[^\x20\t\r\n\f]+/g;function tt(e){return e}function nt(e){throw e}function rt(e,t,n,r){var i;try{e&&m(i=e.promise)?i.call(e).done(t).fail(n):e&&m(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}C.Callbacks=function(r){var e,n;r="string"==typeof r?(e=r,n={},C.each(e.match(q)||[],function(e,t){n[t]=!0}),n):C.extend({},r);function i(){for(s=s||r.once,a=o=!0;l.length;c=-1)for(t=l.shift();++c<u.length;)!1===u[c].apply(t[0],t[1])&&r.stopOnFalse&&(c=u.length,t=!1);r.memory||(t=!1),o=!1,s&&(u=t?[]:"")}var o,t,a,s,u=[],l=[],c=-1,f={add:function(){return u&&(t&&!o&&(c=u.length-1,l.push(t)),function n(e){C.each(e,function(e,t){m(t)?r.unique&&f.has(t)||u.push(t):t&&t.length&&"string"!==G(t)&&n(t)})}(arguments),t)&&!o&&i(),this},remove:function(){return C.each(arguments,function(e,t){for(var n;-1<(n=C.inArray(t,u,n));)u.splice(n,1),n<=c&&c--}),this},has:function(e){return e?-1<C.inArray(e,u):0<u.length},empty:function(){return u=u&&[],this},disable:function(){return s=l=[],u=t="",this},disabled:function(){return!u},lock:function(){return s=l=[],t||o||(u=t=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),o)||i(),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!a}};return f},C.extend({Deferred:function(e){var o=[["notify","progress",C.Callbacks("memory"),C.Callbacks("memory"),2],["resolve","done",C.Callbacks("once memory"),C.Callbacks("once memory"),0,"resolved"],["reject","fail",C.Callbacks("once memory"),C.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},pipe:function(){var i=arguments;return C.Deferred(function(r){C.each(o,function(e,t){var n=m(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&m(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){function e(){var e,t;if(!(i<u)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,m(t)?s?t.call(e,l(u,o,tt,s),l(u,o,nt,s)):(u++,t.call(e,l(u,o,tt,s),l(u,o,nt,s),l(u,o,tt,o.notifyWith))):(a!==tt&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}}var n=this,r=arguments,t=s?e:function(){try{e()}catch(e){C.Deferred.exceptionHook&&C.Deferred.exceptionHook(e,t.error),u<=i+1&&(a!==nt&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(C.Deferred.getErrorHook?t.error=C.Deferred.getErrorHook():C.Deferred.getStackHook&&(t.error=C.Deferred.getStackHook()),w.setTimeout(t))}}return C.Deferred(function(e){o[0][3].add(l(0,e,m(r)?r:tt,e.notifyWith)),o[1][3].add(l(0,e,m(t)?t:tt)),o[2][3].add(l(0,e,m(n)?n:nt))}).promise()},promise:function(e){return null!=e?C.extend(e,a):a}},s={};return C.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){function t(t){return function(e){i[t]=this,o[t]=1<arguments.length?s.call(arguments):e,--n||a.resolveWith(i,o)}}var n=arguments.length,r=n,i=Array(r),o=s.call(arguments),a=C.Deferred();if(n<=1&&(rt(e,a.done(t(r)).resolve,a.reject,!n),"pending"===a.state()||m(o[r]&&o[r].then)))return a.then();for(;r--;)rt(o[r],t(r),a.reject);return a.promise()}});var it=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,ot=(C.Deferred.exceptionHook=function(e,t){w.console&&w.console.warn&&e&&it.test(e.name)&&w.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},C.readyException=function(e){w.setTimeout(function(){throw e})},C.Deferred());function at(){T.removeEventListener("DOMContentLoaded",at),w.removeEventListener("load",at),C.ready()}C.fn.ready=function(e){return ot.then(e).catch(function(e){C.readyException(e)}),this},C.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--C.readyWait:C.isReady)||(C.isReady=!0)!==e&&0<--C.readyWait||ot.resolveWith(T,[C])}}),C.ready.then=ot.then,"complete"===T.readyState||"loading"!==T.readyState&&!T.documentElement.doScroll?w.setTimeout(C.ready):(T.addEventListener("DOMContentLoaded",at),w.addEventListener("load",at));function c(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===G(n))for(s in i=!0,n)c(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,m(r)||(a=!0),t=l?a?(t.call(e,r),null):(l=t,function(e,t,n){return l.call(C(e),n)}):t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o}var st=/^-ms-/,ut=/-([a-z])/g;function lt(e,t){return t.toUpperCase()}function L(e){return e.replace(st,"ms-").replace(ut,lt)}function ct(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function ft(){this.expando=C.expando+ft.uid++}ft.uid=1,ft.prototype={cache:function(e){var t=e[this.expando];return t||(t={},ct(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[L(t)]=n;else for(r in t)i[L(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][L(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(L):(t=L(t))in r?[t]:t.match(q)||[]).length;for(;n--;)delete r[t[n]]}void 0!==t&&!C.isEmptyObject(r)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!C.isEmptyObject(e)}};var v=new ft,l=new ft,pt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,dt=/[A-Z]/g;function ht(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(dt,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:pt.test(i)?JSON.parse(i):i)}catch(e){}l.set(e,t,n)}else n=void 0;return n}C.extend({hasData:function(e){return l.hasData(e)||v.hasData(e)},data:function(e,t,n){return l.access(e,t,n)},removeData:function(e,t){l.remove(e,t)},_data:function(e,t,n){return v.access(e,t,n)},_removeData:function(e,t){v.remove(e,t)}}),C.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){l.set(this,n)}):c(this,function(e){var t;if(o&&void 0===e)return void 0!==(t=l.get(o,n))||void 0!==(t=ht(o,n))?t:void 0;this.each(function(){l.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=l.get(o),1===o.nodeType)&&!v.get(o,"hasDataAttrs")){for(t=a.length;t--;)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=L(r.slice(5)),ht(o,r,i[r]));v.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){l.remove(this,e)})}}),C.extend({queue:function(e,t,n){var r;if(e)return r=v.get(e,t=(t||"fx")+"queue"),n&&(!r||Array.isArray(n)?r=v.access(e,t,C.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=C.queue(e,t),r=n.length,i=n.shift(),o=C._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){C.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return v.get(e,n)||v.access(e,n,{empty:C.Callbacks("once memory").add(function(){v.remove(e,[t+"queue",n])})})}}),C.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?C.queue(this[0],t):void 0===n?this:this.each(function(){var e=C.queue(this,t,n);C._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&C.dequeue(this,t)})},dequeue:function(e){return this.each(function(){C.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--i||o.resolveWith(a,[a])}var r,i=1,o=C.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(r=v.get(a[s],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});function gt(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&xt(e)&&"none"===C.css(e,"display")}var e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,yt=new RegExp("^(?:([+-])=|)("+e+")([a-z%]*)$","i"),vt=["Top","Right","Bottom","Left"],mt=T.documentElement,xt=function(e){return C.contains(e.ownerDocument,e)},bt={composed:!0};mt.getRootNode&&(xt=function(e){return C.contains(e.ownerDocument,e)||e.getRootNode(bt)===e.ownerDocument});function wt(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return C.css(e,t,"")},u=s(),l=n&&n[3]||(C.cssNumber[t]?"":"px"),c=e.nodeType&&(C.cssNumber[t]||"px"!==l&&+u)&&yt.exec(C.css(e,t));if(c&&c[3]!==l){for(l=l||c[3],c=+(u/=2)||1;a--;)C.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;C.style(e,t,(c*=2)+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r)&&(r.unit=l,r.start=c,r.end=i),i}var Tt={};function Ct(e,t){for(var n,r,i,o,a,s=[],u=0,l=e.length;u<l;u++)(r=e[u]).style&&(n=r.style.display,t?("none"===n&&(s[u]=v.get(r,"display")||null,s[u]||(r.style.display="")),""===r.style.display&&gt(r)&&(s[u]=(a=o=void 0,o=(i=r).ownerDocument,(a=Tt[i=i.nodeName])||(o=o.body.appendChild(o.createElement(i)),a=C.css(o,"display"),o.parentNode.removeChild(o),Tt[i]=a="none"===a?"block":a),a))):"none"!==n&&(s[u]="none",v.set(r,"display",n)));for(u=0;u<l;u++)null!=s[u]&&(e[u].style.display=s[u]);return e}C.fn.extend({show:function(){return Ct(this,!0)},hide:function(){return Ct(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){gt(this)?C(this).show():C(this).hide()})}});var kt=/^(?:checkbox|radio)$/i,Et=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,St=/^$|^module$|\/(?:java|ecma)script/i,h=(o=T.createDocumentFragment().appendChild(T.createElement("div")),(a=T.createElement("input")).setAttribute("type","radio"),a.setAttribute("checked","checked"),a.setAttribute("name","t"),o.appendChild(a),g.checkClone=o.cloneNode(!0).cloneNode(!0).lastChild.checked,o.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!o.cloneNode(!0).lastChild.defaultValue,o.innerHTML="<option></option>",g.option=!!o.lastChild,{thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]});function y(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&b(e,t)?C.merge([e],n):n}function jt(e,t){for(var n=0,r=e.length;n<r;n++)v.set(e[n],"globalEval",!t||v.get(t[n],"globalEval"))}h.tbody=h.tfoot=h.colgroup=h.caption=h.thead,h.th=h.td,g.option||(h.optgroup=h.option=[1,"<select multiple='multiple'>","</select>"]);var At=/<|&#?\w+;/;function Dt(e,t,n,r,i){for(var o,a,s,u,l,c=t.createDocumentFragment(),f=[],p=0,d=e.length;p<d;p++)if((o=e[p])||0===o)if("object"===G(o))C.merge(f,o.nodeType?[o]:o);else if(At.test(o)){for(a=a||c.appendChild(t.createElement("div")),s=(Et.exec(o)||["",""])[1].toLowerCase(),s=h[s]||h._default,a.innerHTML=s[1]+C.htmlPrefilter(o)+s[2],l=s[0];l--;)a=a.lastChild;C.merge(f,a.childNodes),(a=c.firstChild).textContent=""}else f.push(t.createTextNode(o));for(c.textContent="",p=0;o=f[p++];)if(r&&-1<C.inArray(o,r))i&&i.push(o);else if(u=xt(o),a=y(c.appendChild(o),"script"),u&&jt(a),n)for(l=0;o=a[l++];)St.test(o.type||"")&&n.push(o);return c}var Nt=/^([^.]*)(?:\.(.+)|)/;function qt(){return!0}function Lt(){return!1}function Ht(e,t,n,r,i,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Ht(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Lt;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return C().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=C.guid++)),e.each(function(){C.event.add(this,t,i,r,n)})}function Ot(e,r,t){t?(v.set(e,r,!1),C.event.add(e,r,{namespace:!1,handler:function(e){var t,n=v.get(this,r);if(1&e.isTrigger&&this[r]){if(n)(C.event.special[r]||{}).delegateType&&e.stopPropagation();else if(n=s.call(arguments),v.set(this,r,n),this[r](),t=v.get(this,r),v.set(this,r,!1),n!==t)return e.stopImmediatePropagation(),e.preventDefault(),t}else n&&(v.set(this,r,C.event.trigger(n[0],n.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=qt)}})):void 0===v.get(e,r)&&C.event.add(e,r,qt)}C.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,f,p,d,h=v.get(t);if(ct(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&C.find.matchesSelector(mt,i),n.guid||(n.guid=C.guid++),s=(s=h.events)||(h.events=Object.create(null)),a=(a=h.handle)||(h.handle=function(e){return void 0!==C&&C.event.triggered!==e.type?C.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match(q)||[""]).length;u--;)f=d=(p=Nt.exec(e[u])||[])[1],p=(p[2]||"").split(".").sort(),f&&(l=C.event.special[f]||{},f=(i?l.delegateType:l.bindType)||f,l=C.event.special[f]||{},d=C.extend({type:f,origType:d,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&C.expr.match.needsContext.test(i),namespace:p.join(".")},o),(c=s[f])||((c=s[f]=[]).delegateCount=0,l.setup&&!1!==l.setup.call(t,r,p,a))||t.addEventListener&&t.addEventListener(f,a),l.add&&(l.add.call(t,d),d.handler.guid||(d.handler.guid=n.guid)),i?c.splice(c.delegateCount++,0,d):c.push(d),C.event.global[f]=!0)},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,y=v.hasData(e)&&v.get(e);if(y&&(u=y.events)){for(l=(t=(t||"").match(q)||[""]).length;l--;)if(d=g=(s=Nt.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),d){for(f=C.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;o--;)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,y.handle)||C.removeEvent(e,d,y.handle),delete u[d])}else for(d in u)C.event.remove(e,d+t[l],n,r,!0);C.isEmptyObject(u)&&v.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a=new Array(arguments.length),s=C.event.fix(e),e=(v.get(this,"events")||Object.create(null))[s.type]||[],u=C.event.special[s.type]||{};for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];if(s.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,s)){for(o=C.event.handlers.call(this,s,e),t=0;(r=o[t++])&&!s.isPropagationStopped();)for(s.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!1!==i.namespace&&!s.rnamespace.test(i.namespace)||(s.handleObj=i,s.data=i.data,void 0!==(i=((C.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,a))&&!1===(s.result=i)&&(s.preventDefault(),s.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<C(i,this).index(l):C.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(C.Event.prototype,t,{enumerable:!0,configurable:!0,get:m(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[C.expando]?e:new C.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return kt.test(e.type)&&e.click&&b(e,"input")&&Ot(e,"click",!0),!1},trigger:function(e){e=this||e;return kt.test(e.type)&&e.click&&b(e,"input")&&Ot(e,"click"),!0},_default:function(e){e=e.target;return kt.test(e.type)&&e.click&&b(e,"input")&&v.get(e,"click")||b(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},C.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},C.Event=function(e,t){if(!(this instanceof C.Event))return new C.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?qt:Lt,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&C.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[C.expando]=!0},C.Event.prototype={constructor:C.Event,isDefaultPrevented:Lt,isPropagationStopped:Lt,isImmediatePropagationStopped:Lt,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=qt,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=qt,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=qt,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},C.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},C.event.addProp),C.each({focus:"focusin",blur:"focusout"},function(r,i){function o(e){var t,n;T.documentMode?(t=v.get(this,"handle"),(n=C.event.fix(e)).type="focusin"===e.type?"focus":"blur",n.isSimulated=!0,t(e),n.target===n.currentTarget&&t(n)):C.event.simulate(i,e.target,C.event.fix(e))}C.event.special[r]={setup:function(){var e;if(Ot(this,r,!0),!T.documentMode)return!1;(e=v.get(this,i))||this.addEventListener(i,o),v.set(this,i,(e||0)+1)},trigger:function(){return Ot(this,r),!0},teardown:function(){var e;if(!T.documentMode)return!1;(e=v.get(this,i)-1)?v.set(this,i,e):(this.removeEventListener(i,o),v.remove(this,i))},_default:function(e){return v.get(e.target,r)},delegateType:i},C.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=T.documentMode?this:e,n=v.get(t,i);n||(T.documentMode?this.addEventListener(i,o):e.addEventListener(r,o,!0)),v.set(t,i,(n||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=T.documentMode?this:e,n=v.get(t,i)-1;n?v.set(t,i,n):(T.documentMode?this.removeEventListener(i,o):e.removeEventListener(r,o,!0),v.remove(t,i))}}}),C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){C.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||C.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),C.fn.extend({on:function(e,t,n,r){return Ht(this,e,t,n,r)},one:function(e,t,n,r){return Ht(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)r=e.handleObj,C(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler);else{if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Lt),this.each(function(){C.event.remove(this,e,n,t)});for(i in e)this.off(i,t,e[i])}return this}});var Pt=/<script|<style|<link/i,Mt=/checked\s*(?:[^=]|=\s*.checked.)/i,Rt=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function It(e,t){return b(e,"table")&&b(11!==t.nodeType?t:t.firstChild,"tr")&&C(e).children("tbody")[0]||e}function Ft(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Wt(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function $t(e,t){var n,r,i,o;if(1===t.nodeType){if(v.hasData(e)&&(o=v.get(e).events))for(i in v.remove(t,"handle events"),o)for(n=0,r=o[i].length;n<r;n++)C.event.add(t,i,o[i][n]);l.hasData(e)&&(e=l.access(e),e=C.extend({},e),l.set(t,e))}}function Bt(n,r,i,o){r=F(r);var e,t,a,s,u,l,c=0,f=n.length,p=f-1,d=r[0],h=m(d);if(h||1<f&&"string"==typeof d&&!g.checkClone&&Mt.test(d))return n.each(function(e){var t=n.eq(e);h&&(r[0]=d.call(this,e,t.html())),Bt(t,r,i,o)});if(f&&(t=(e=Dt(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=C.map(y(e,"script"),Ft)).length;c<f;c++)u=e,c!==p&&(u=C.clone(u,!0,!0),s)&&C.merge(a,y(u,"script")),i.call(n[c],u,c);if(s)for(l=a[a.length-1].ownerDocument,C.map(a,Wt),c=0;c<s;c++)u=a[c],St.test(u.type||"")&&!v.access(u,"globalEval")&&C.contains(l,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?C._evalUrl&&!u.noModule&&C._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},l):V(u.textContent.replace(Rt,""),u,l))}return n}function _t(e,t,n){for(var r,i=t?C.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||C.cleanData(y(r)),r.parentNode&&(n&&xt(r)&&jt(y(r,"script")),r.parentNode.removeChild(r));return e}C.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),f=xt(e);if(!(g.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||C.isXMLDoc(e)))for(a=y(c),r=0,i=(o=y(e)).length;r<i;r++)s=o[r],u=a[r],l=void 0,"input"===(l=u.nodeName.toLowerCase())&&kt.test(s.type)?u.checked=s.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=s.defaultValue);if(t)if(n)for(o=o||y(e),a=a||y(c),r=0,i=o.length;r<i;r++)$t(o[r],a[r]);else $t(e,c);return 0<(a=y(c,"script")).length&&jt(a,!f&&y(e,"script")),c},cleanData:function(e){for(var t,n,r,i=C.event.special,o=0;void 0!==(n=e[o]);o++)if(ct(n)){if(t=n[v.expando]){if(t.events)for(r in t.events)i[r]?C.event.remove(n,r):C.removeEvent(n,r,t.handle);n[v.expando]=void 0}n[l.expando]&&(n[l.expando]=void 0)}}}),C.fn.extend({detach:function(e){return _t(this,e,!0)},remove:function(e){return _t(this,e)},text:function(e){return c(this,function(e){return void 0===e?C.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Bt(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||It(this,e).appendChild(e)})},prepend:function(){return Bt(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=It(this,e)).insertBefore(e,t.firstChild)})},before:function(){return Bt(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Bt(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(C.cleanData(y(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return C.clone(this,e,t)})},html:function(e){return c(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Pt.test(e)&&!h[(Et.exec(e)||["",""])[1].toLowerCase()]){e=C.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(C.cleanData(y(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Bt(this,arguments,function(e){var t=this.parentNode;C.inArray(this,n)<0&&(C.cleanData(y(this)),t)&&t.replaceChild(e,this)},n)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){C.fn[e]=function(e){for(var t,n=[],r=C(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),C(r[o])[a](t),W.apply(n,t.get());return this.pushStack(n)}});function Ut(e){var t=e.ownerDocument.defaultView;return(t=t&&t.opener?t:w).getComputedStyle(e)}function zt(e,t,n){var r,i={};for(r in t)i[r]=e.style[r],e.style[r]=t[r];for(r in n=n.call(e),t)e.style[r]=i[r];return n}var Xt,Vt,Gt,Yt,Qt,Jt,Kt,H,Zt=new RegExp("^("+e+")(?!px)[a-z%]+$","i"),en=/^--/,tn=new RegExp(vt.join("|"),"i");function nn(){var e;H&&(Kt.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",H.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",mt.appendChild(Kt).appendChild(H),e=w.getComputedStyle(H),Xt="1%"!==e.top,Jt=12===rn(e.marginLeft),H.style.right="60%",Yt=36===rn(e.right),Vt=36===rn(e.width),H.style.position="absolute",Gt=12===rn(H.offsetWidth/3),mt.removeChild(Kt),H=null)}function rn(e){return Math.round(parseFloat(e))}function on(e,t,n){var r,i=en.test(t),o=e.style;return(n=n||Ut(e))&&(r=n.getPropertyValue(t)||n[t],""!==(r=i?r&&(r.replace(ee,"$1")||void 0):r)||xt(e)||(r=C.style(e,t)),!g.pixelBoxStyles())&&Zt.test(r)&&tn.test(t)&&(i=o.width,e=o.minWidth,t=o.maxWidth,o.minWidth=o.maxWidth=o.width=r,r=n.width,o.width=i,o.minWidth=e,o.maxWidth=t),void 0!==r?r+"":r}function an(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}Kt=T.createElement("div"),(H=T.createElement("div")).style&&(H.style.backgroundClip="content-box",H.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle="content-box"===H.style.backgroundClip,C.extend(g,{boxSizingReliable:function(){return nn(),Vt},pixelBoxStyles:function(){return nn(),Yt},pixelPosition:function(){return nn(),Xt},reliableMarginLeft:function(){return nn(),Jt},scrollboxSize:function(){return nn(),Gt},reliableTrDimensions:function(){var e,t,n;return null==Qt&&(e=T.createElement("table"),t=T.createElement("tr"),n=T.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",mt.appendChild(e).appendChild(t).appendChild(n),n=w.getComputedStyle(t),Qt=parseInt(n.height,10)+parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10)===t.offsetHeight,mt.removeChild(e)),Qt}}));var sn=["Webkit","Moz","ms"],un=T.createElement("div").style,ln={};function cn(e){var t=C.cssProps[e]||ln[e];return t||(e in un?e:ln[e]=(e=>{for(var t=e[0].toUpperCase()+e.slice(1),n=sn.length;n--;)if((e=sn[n]+t)in un)return e})(e)||e)}var fn=/^(none|table(?!-c[ea]).+)/,pn={position:"absolute",visibility:"hidden",display:"block"},dn={letterSpacing:"0",fontWeight:"400"};function hn(e,t,n){var r=yt.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function gn(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=C.css(e,n+vt[a],!0,i)),r?("content"===n&&(u-=C.css(e,"padding"+vt[a],!0,i)),"margin"!==n&&(u-=C.css(e,"border"+vt[a]+"Width",!0,i))):(u+=C.css(e,"padding"+vt[a],!0,i),"padding"!==n?u+=C.css(e,"border"+vt[a]+"Width",!0,i):s+=C.css(e,"border"+vt[a]+"Width",!0,i));return!r&&0<=o&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u+l}function yn(e,t,n){var r=Ut(e),i=(!g.boxSizingReliable()||n)&&"border-box"===C.css(e,"boxSizing",!1,r),o=i,a=on(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Zt.test(a)){if(!n)return a;a="auto"}return(!g.boxSizingReliable()&&i||!g.reliableTrDimensions()&&b(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===C.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===C.css(e,"boxSizing",!1,r),o=s in e)&&(a=e[s]),(a=parseFloat(a)||0)+gn(e,t,n||(i?"border":"content"),o,r,a)+"px"}function O(e,t,n,r,i){return new O.prototype.init(e,t,n,r,i)}C.extend({cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=on(e,"opacity"))?"1":t}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=L(t),u=en.test(t),l=e.style;if(u||(t=cn(s)),a=C.cssHooks[t]||C.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"===(o=typeof n)&&(i=yt.exec(n))&&i[1]&&(n=wt(e,t,i),o="number"),null==n||n!=n||("number"!==o||u||(n+=i&&i[3]||(C.cssNumber[s]?"":"px")),g.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r)))||(u?l.setProperty(t,n):l[t]=n)}},css:function(e,t,n,r){var i,o=L(t);return en.test(t)||(t=cn(o)),"normal"===(i=void 0===(i=(o=C.cssHooks[t]||C.cssHooks[o])&&"get"in o?o.get(e,!0,n):i)?on(e,t,r):i)&&t in dn&&(i=dn[t]),(""===n||n)&&(o=parseFloat(i),!0===n||isFinite(o))?o||0:i}}),C.each(["height","width"],function(e,a){C.cssHooks[a]={get:function(e,t,n){if(t)return!fn.test(C.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?yn(e,a,n):zt(e,pn,function(){return yn(e,a,n)})},set:function(e,t,n){var r=Ut(e),i=!g.scrollboxSize()&&"absolute"===r.position,o=(i||n)&&"border-box"===C.css(e,"boxSizing",!1,r),n=n?gn(e,a,n,o,r):0;return o&&i&&(n-=Math.ceil(e["offset"+a[0].toUpperCase()+a.slice(1)]-parseFloat(r[a])-gn(e,a,"border",!1,r)-.5)),n&&(o=yt.exec(t))&&"px"!==(o[3]||"px")&&(e.style[a]=t,t=C.css(e,a)),hn(0,t,n)}}}),C.cssHooks.marginLeft=an(g.reliableMarginLeft,function(e,t){if(t)return(parseFloat(on(e,"marginLeft"))||e.getBoundingClientRect().left-zt(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),C.each({margin:"",padding:"",border:"Width"},function(i,o){C.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+vt[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(C.cssHooks[i+o].set=hn)}),C.fn.extend({css:function(e,t){return c(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Ut(e),i=t.length;a<i;a++)o[t[a]]=C.css(e,t[a],!1,r);return o}return void 0!==n?C.style(e,t,n):C.css(e,t)},e,t,1<arguments.length)}}),((C.Tween=O).prototype={constructor:O,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||C.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(C.cssNumber[n]?"":"px")},cur:function(){var e=O.propHooks[this.prop];return(e&&e.get?e:O.propHooks._default).get(this)},run:function(e){var t,n=O.propHooks[this.prop];return this.options.duration?this.pos=t=C.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:O.propHooks._default).set(this),this}}).init.prototype=O.prototype,(O.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=C.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){C.fx.step[e.prop]?C.fx.step[e.prop](e):1!==e.elem.nodeType||!C.cssHooks[e.prop]&&null==e.elem.style[cn(e.prop)]?e.elem[e.prop]=e.now:C.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=O.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},C.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},C.fx=O.prototype.init,C.fx.step={};var vn,mn,xn=/^(?:toggle|show|hide)$/,bn=/queueHooks$/;function wn(){mn&&(!1===T.hidden&&w.requestAnimationFrame?w.requestAnimationFrame(wn):w.setTimeout(wn,C.fx.interval),C.fx.tick())}function Tn(){return w.setTimeout(function(){vn=void 0}),vn=Date.now()}function Cn(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=vt[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function kn(e,t,n){for(var r,i=(P.tweeners[t]||[]).concat(P.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function P(i,e,t){var n,o,r,a,s,u,l,c=0,f=P.prefilters.length,p=C.Deferred().always(function(){delete d.elem}),d=function(){if(!o){for(var e=vn||Tn(),e=Math.max(0,h.startTime+h.duration-e),t=1-(e/h.duration||0),n=0,r=h.tweens.length;n<r;n++)h.tweens[n].run(t);if(p.notifyWith(i,[h,t,e]),t<1&&r)return e;r||p.notifyWith(i,[h,1,0]),p.resolveWith(i,[h])}return!1},h=p.promise({elem:i,props:C.extend({},e),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},t),originalProperties:e,originalOptions:t,startTime:vn||Tn(),duration:t.duration,tweens:[],createTween:function(e,t){t=C.Tween(i,h.opts,e,t,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(t),t},stop:function(e){var t=0,n=e?h.tweens.length:0;if(!o){for(o=!0;t<n;t++)h.tweens[t].run(1);e?(p.notifyWith(i,[h,1,0]),p.resolveWith(i,[h,e])):p.rejectWith(i,[h,e])}return this}}),g=h.props,y=g,v=h.opts.specialEasing;for(r in y)if(s=v[a=L(r)],u=y[r],Array.isArray(u)&&(s=u[1],u=y[r]=u[0]),r!==a&&(y[a]=u,delete y[r]),(l=C.cssHooks[a])&&"expand"in l)for(r in u=l.expand(u),delete y[a],u)r in y||(y[r]=u[r],v[r]=s);else v[a]=s;for(;c<f;c++)if(n=P.prefilters[c].call(h,i,g,h.opts))return m(n.stop)&&(C._queueHooks(h.elem,h.opts.queue).stop=n.stop.bind(n)),n;return C.map(g,kn,h),m(h.opts.start)&&h.opts.start.call(i,h),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always),C.fx.timer(C.extend(d,{elem:i,anim:h,queue:h.opts.queue})),h}C.Animation=C.extend(P,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return wt(n.elem,e,yt.exec(t),n),n}]},tweener:function(e,t){for(var n,r=0,i=(e=m(e)?(t=e,["*"]):e.match(q)).length;r<i;r++)n=e[r],P.tweeners[n]=P.tweeners[n]||[],P.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c="width"in t||"height"in t,f=this,p={},d=e.style,h=e.nodeType&&gt(e),g=v.get(e,"fxshow");for(r in n.queue||(null==(a=C._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,f.always(function(){f.always(function(){a.unqueued--,C.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],xn.test(i)){if(delete t[r],o=o||"toggle"===i,i===(h?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;h=!0}p[r]=g&&g[r]||C.style(e,r)}if((u=!C.isEmptyObject(t))||!C.isEmptyObject(p))for(r in c&&1===e.nodeType&&(n.overflow=[d.overflow,d.overflowX,d.overflowY],null==(l=g&&g.display)&&(l=v.get(e,"display")),"none"===(c=C.css(e,"display"))&&(l?c=l:(Ct([e],!0),l=e.style.display||l,c=C.css(e,"display"),Ct([e]))),"inline"===c||"inline-block"===c&&null!=l)&&"none"===C.css(e,"float")&&(u||(f.done(function(){d.display=l}),null==l&&(c=d.display,l="none"===c?"":c)),d.display="inline-block"),n.overflow&&(d.overflow="hidden",f.always(function(){d.overflow=n.overflow[0],d.overflowX=n.overflow[1],d.overflowY=n.overflow[2]})),u=!1,p)u||(g?"hidden"in g&&(h=g.hidden):g=v.access(e,"fxshow",{display:l}),o&&(g.hidden=!h),h&&Ct([e],!0),f.done(function(){for(r in h||Ct([e]),v.remove(e,"fxshow"),p)C.style(e,r,p[r])})),u=kn(h?g[r]:0,r,f),r in g||(g[r]=u.start,h&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?P.prefilters.unshift(e):P.prefilters.push(e)}}),C.speed=function(e,t,n){var r=e&&"object"==typeof e?C.extend({},e):{complete:n||!n&&t||m(e)&&e,duration:e,easing:n&&t||t&&!m(t)&&t};return C.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in C.fx.speeds?r.duration=C.fx.speeds[r.duration]:r.duration=C.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){m(r.old)&&r.old.call(this),r.queue&&C.dequeue(this,r.queue)},r},C.fn.extend({fadeTo:function(e,t,n,r){return this.filter(gt).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){function i(){var e=P(this,C.extend({},t),a);(o||v.get(this,"finish"))&&e.stop(!0)}var o=C.isEmptyObject(t),a=C.speed(e,n,r);return i.finish=i,o||!1===a.queue?this.each(i):this.queue(a.queue,i)},stop:function(i,e,o){function a(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=C.timers,r=v.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&bn.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||C.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=v.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=C.timers,o=n?n.length:0;for(t.finish=!0,C.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),C.each(["toggle","show","hide"],function(e,r){var i=C.fn[r];C.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(Cn(r,!0),e,t,n)}}),C.each({slideDown:Cn("show"),slideUp:Cn("hide"),slideToggle:Cn("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){C.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),C.timers=[],C.fx.tick=function(){var e,t=0,n=C.timers;for(vn=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||C.fx.stop(),vn=void 0},C.fx.timer=function(e){C.timers.push(e),C.fx.start()},C.fx.interval=13,C.fx.start=function(){mn||(mn=!0,wn())},C.fx.stop=function(){mn=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(r,e){return r=C.fx&&C.fx.speeds[r]||r,this.queue(e=e||"fx",function(e,t){var n=w.setTimeout(e,r);t.stop=function(){w.clearTimeout(n)}})},a=T.createElement("input"),o=T.createElement("select").appendChild(T.createElement("option")),a.type="checkbox",g.checkOn=""!==a.value,g.optSelected=o.selected,(a=T.createElement("input")).value="t",a.type="radio",g.radioValue="t"===a.value;var En,Sn=C.expr.attrHandle,jn=(C.fn.extend({attr:function(e,t){return c(this,C.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){C.removeAttr(this,e)})}}),C.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?C.prop(e,t,n):(1===o&&C.isXMLDoc(e)||(i=C.attrHooks[t.toLowerCase()]||(C.expr.match.bool.test(t)?En:void 0)),void 0!==n?null===n?void C.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):!(i&&"get"in i&&null!==(r=i.get(e,t)))&&null==(r=C.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){var n;if(!g.radioValue&&"radio"===t&&b(e,"input"))return n=e.value,e.setAttribute("type",t),n&&(e.value=n),t}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(q);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),En={set:function(e,t,n){return!1===t?C.removeAttr(e,n):e.setAttribute(n,n),n}},C.each(C.expr.match.bool.source.match(/\w+/g),function(e,t){var a=Sn[t]||C.find.attr;Sn[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=Sn[o],Sn[o]=r,r=null!=a(e,t,n)?o:null,Sn[o]=i),r}}),/^(?:input|select|textarea|button)$/i),An=/^(?:a|area)$/i;function Dn(e){return(e.match(q)||[]).join(" ")}function Nn(e){return e.getAttribute&&e.getAttribute("class")||""}function qn(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(q)||[]}C.fn.extend({prop:function(e,t){return c(this,C.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[C.propFix[e]||e]})}}),C.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&C.isXMLDoc(e)||(t=C.propFix[t]||t,i=C.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=C.find.attr(e,"tabindex");return t?parseInt(t,10):jn.test(e.nodeName)||An.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),g.optSelected||(C.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode)&&e.parentNode.selectedIndex}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){C.propFix[this.toLowerCase()]=this}),C.fn.extend({addClass:function(t){var e,n,r,i,o,a;return m(t)?this.each(function(e){C(this).addClass(t.call(this,e,Nn(this)))}):(e=qn(t)).length?this.each(function(){if(r=Nn(this),n=1===this.nodeType&&" "+Dn(r)+" "){for(o=0;o<e.length;o++)i=e[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");a=Dn(n),r!==a&&this.setAttribute("class",a)}}):this},removeClass:function(t){var e,n,r,i,o,a;return m(t)?this.each(function(e){C(this).removeClass(t.call(this,e,Nn(this)))}):arguments.length?(e=qn(t)).length?this.each(function(){if(r=Nn(this),n=1===this.nodeType&&" "+Dn(r)+" "){for(o=0;o<e.length;o++)for(i=e[o];-1<n.indexOf(" "+i+" ");)n=n.replace(" "+i+" "," ");a=Dn(n),r!==a&&this.setAttribute("class",a)}}):this:this.attr("class","")},toggleClass:function(t,n){var e,r,i,o,a=typeof t,s="string"==a||Array.isArray(t);return m(t)?this.each(function(e){C(this).toggleClass(t.call(this,e,Nn(this),n),n)}):"boolean"==typeof n&&s?n?this.addClass(t):this.removeClass(t):(e=qn(t),this.each(function(){if(s)for(o=C(this),i=0;i<e.length;i++)r=e[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==t&&"boolean"!=a||((r=Nn(this))&&v.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",!r&&!1!==t&&v.get(this,"__className__")||""))}))},hasClass:function(e){for(var t,n=0,r=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+Dn(Nn(t))+" ").indexOf(r))return!0;return!1}});function Ln(e){e.stopPropagation()}var Hn=/\r/g,On=(C.fn.extend({val:function(t){var n,e,r,i=this[0];return arguments.length?(r=m(t),this.each(function(e){1!==this.nodeType||(null==(e=r?t.call(this,e,C(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=C.map(e,function(e){return null==e?"":e+""})),(n=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value"))||(this.value=e)})):i?(n=C.valHooks[i.type]||C.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(i,"value"))?e:"string"==typeof(e=i.value)?e.replace(Hn,""):null==e?"":e:void 0}}),C.extend({valHooks:{option:{get:function(e){var t=C.find.attr(e,"value");return null!=t?t:Dn(C.text(e))}},select:{get:function(e){for(var t,n=e.options,r=e.selectedIndex,i="select-one"===e.type,o=i?null:[],a=i?r+1:n.length,s=r<0?a:i?r:0;s<a;s++)if(((t=n[s]).selected||s===r)&&!t.disabled&&(!t.parentNode.disabled||!b(t.parentNode,"optgroup"))){if(t=C(t).val(),i)return t;o.push(t)}return o},set:function(e,t){for(var n,r,i=e.options,o=C.makeArray(t),a=i.length;a--;)((r=i[a]).selected=-1<C.inArray(C.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),C.each(["radio","checkbox"],function(){C.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<C.inArray(C(e).val(),t)}},g.checkOn||(C.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),w.location),Pn={guid:Date.now()},Mn=/\?/,Rn=(C.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new w.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||C.error("Invalid XML: "+(n?C.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t},/^(?:focusinfocus|focusoutblur)$/),In=(C.extend(C.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,f=[n||T],p=_.call(e,"type")?e.type:e,d=_.call(e,"namespace")?e.namespace.split("."):[],h=c=o=n=n||T;if(3!==n.nodeType&&8!==n.nodeType&&!Rn.test(p+C.event.triggered)&&(-1<p.indexOf(".")&&(p=(d=p.split(".")).shift(),d.sort()),s=p.indexOf(":")<0&&"on"+p,(e=e[C.expando]?e:new C.Event(p,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=d.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:C.makeArray(t,[e]),l=C.event.special[p]||{},r||!l.trigger||!1!==l.trigger.apply(n,t))){if(!r&&!l.noBubble&&!R(n)){for(a=l.delegateType||p,Rn.test(a+p)||(h=h.parentNode);h;h=h.parentNode)f.push(h),o=h;o===(n.ownerDocument||T)&&f.push(o.defaultView||o.parentWindow||w)}for(i=0;(h=f[i++])&&!e.isPropagationStopped();)c=h,e.type=1<i?a:l.bindType||p,(u=(v.get(h,"events")||Object.create(null))[e.type]&&v.get(h,"handle"))&&u.apply(h,t),(u=s&&h[s])&&u.apply&&ct(h)&&(e.result=u.apply(h,t),!1===e.result)&&e.preventDefault();return e.type=p,r||e.isDefaultPrevented()||l._default&&!1!==l._default.apply(f.pop(),t)||!ct(n)||s&&m(n[p])&&!R(n)&&((o=n[s])&&(n[s]=null),C.event.triggered=p,e.isPropagationStopped()&&c.addEventListener(p,Ln),n[p](),e.isPropagationStopped()&&c.removeEventListener(p,Ln),C.event.triggered=void 0,o)&&(n[s]=o),e.result}},simulate:function(e,t,n){n=C.extend(new C.Event,n,{type:e,isSimulated:!0});C.event.trigger(n,null,t)}}),C.fn.extend({trigger:function(e,t){return this.each(function(){C.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return C.event.trigger(e,t,n,!0)}}),/\[\]$/),Fn=/\r?\n/g,Wn=/^(?:submit|button|image|reset|file)$/i,$n=/^(?:input|select|textarea|keygen)/i;C.param=function(e,t){function n(e,t){t=m(t)?t():t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var r,i=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!C.isPlainObject(e))C.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,i,o){if(Array.isArray(e))C.each(e,function(e,t){i||In.test(r)?o(r,t):n(r+"["+("object"==typeof t&&null!=t?e:"")+"]",t,i,o)});else if(i||"object"!==G(e))o(r,e);else for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);return i.join("&")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=C.prop(this,"elements");return e?C.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!C(this).is(":disabled")&&$n.test(this.nodeName)&&!Wn.test(e)&&(this.checked||!kt.test(e))}).map(function(e,t){var n=C(this).val();return null==n?null:Array.isArray(n)?C.map(n,function(e){return{name:t.name,value:e.replace(Fn,"\r\n")}}):{name:t.name,value:n.replace(Fn,"\r\n")}}).get()}});var Bn=/%20/g,_n=/#.*$/,Un=/([?&])_=[^&]*/,zn=/^(.*?):[ \t]*([^\r\n]*)$/gm,Xn=/^(?:GET|HEAD)$/,Vn=/^\/\//,Gn={},Yn={},Qn="*/".concat("*"),Jn=T.createElement("a");function Kn(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(q)||[];if(m(t))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Zn(t,r,i,o){var a={},s=t===Yn;function u(e){var n;return a[e]=!0,C.each(t[e]||[],function(e,t){t=t(r,i,o);return"string"!=typeof t||s||a[t]?s?!(n=t):void 0:(r.dataTypes.unshift(t),u(t),!1)}),n}return u(r.dataTypes[0])||!a["*"]&&u("*")}function er(e,t){var n,r,i=C.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r=r||{})[n]=t[n]);return r&&C.extend(!0,e,r),e}Jn.href=On.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:On.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(On.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Qn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?er(er(e,C.ajaxSettings),t):er(C.ajaxSettings,e)},ajaxPrefilter:Kn(Gn),ajaxTransport:Kn(Yn),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var u,l,c,n,f,p,d,r,h=C.ajaxSetup({},t=t||{}),g=h.context||h,y=h.context&&(g.nodeType||g.jquery)?C(g):C.event,v=C.Deferred(),m=C.Callbacks("once memory"),x=h.statusCode||{},i={},o={},a="canceled",b={readyState:0,getResponseHeader:function(e){var t;if(p){if(!n)for(n={};t=zn.exec(c);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return p?c:null},setRequestHeader:function(e,t){return null==p&&(e=o[e.toLowerCase()]=o[e.toLowerCase()]||e,i[e]=t),this},overrideMimeType:function(e){return null==p&&(h.mimeType=e),this},statusCode:function(e){if(e)if(p)b.always(e[b.status]);else for(var t in e)x[t]=[x[t],e[t]];return this},abort:function(e){e=e||a;return u&&u.abort(e),s(0,e),this}};if(v.promise(b),h.url=((e||h.url||On.href)+"").replace(Vn,On.protocol+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(q)||[""],null==h.crossDomain){e=T.createElement("a");try{e.href=h.url,e.href=e.href,h.crossDomain=Jn.protocol+"//"+Jn.host!=e.protocol+"//"+e.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=C.param(h.data,h.traditional)),Zn(Gn,h,t,b),!p){for(r in(d=C.event&&h.global)&&0==C.active++&&C.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Xn.test(h.type),l=h.url.replace(_n,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Bn,"+")):(e=h.url.slice(l.length),h.data&&(h.processData||"string"==typeof h.data)&&(l+=(Mn.test(l)?"&":"?")+h.data,delete h.data),!1===h.cache&&(l=l.replace(Un,"$1"),e=(Mn.test(l)?"&":"?")+"_="+Pn.guid+++e),h.url=l+e),h.ifModified&&(C.lastModified[l]&&b.setRequestHeader("If-Modified-Since",C.lastModified[l]),C.etag[l])&&b.setRequestHeader("If-None-Match",C.etag[l]),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&b.setRequestHeader("Content-Type",h.contentType),b.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Qn+"; q=0.01":""):h.accepts["*"]),h.headers)b.setRequestHeader(r,h.headers[r]);if(h.beforeSend&&(!1===h.beforeSend.call(g,b,h)||p))return b.abort();if(a="abort",m.add(h.complete),b.done(h.success),b.fail(h.error),u=Zn(Yn,h,t,b)){if(b.readyState=1,d&&y.trigger("ajaxSend",[b,h]),p)return b;h.async&&0<h.timeout&&(f=w.setTimeout(function(){b.abort("timeout")},h.timeout));try{p=!1,u.send(i,s)}catch(e){if(p)throw e;s(-1,e)}}else s(-1,"No Transport")}return b;function s(e,t,n,r){var i,o,a,s=t;p||(p=!0,f&&w.clearTimeout(f),u=void 0,c=r||"",b.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(a=((e,t,n)=>{for(var r,i,o,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a=a||i}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]})(h,b,n)),!r&&-1<C.inArray("script",h.dataTypes)&&C.inArray("json",h.dataTypes)<0&&(h.converters["text script"]=function(){}),a=((e,t,n,r)=>{var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}})(h,a,b,r),r?(h.ifModified&&((n=b.getResponseHeader("Last-Modified"))&&(C.lastModified[l]=n),n=b.getResponseHeader("etag"))&&(C.etag[l]=n),204===e||"HEAD"===h.type?s="nocontent":304===e?s="notmodified":(s=a.state,i=a.data,r=!(o=a.error))):(o=s,!e&&s||(s="error",e<0&&(e=0))),b.status=e,b.statusText=(t||s)+"",r?v.resolveWith(g,[i,s,b]):v.rejectWith(g,[b,s,o]),b.statusCode(x),x=void 0,d&&y.trigger(r?"ajaxSuccess":"ajaxError",[b,h,r?i:o]),m.fireWith(g,[b,s]),d&&(y.trigger("ajaxComplete",[b,h]),--C.active||C.event.trigger("ajaxStop")))}},getJSON:function(e,t,n){return C.get(e,t,n,"json")},getScript:function(e,t){return C.get(e,void 0,t,"script")}}),C.each(["get","post"],function(e,i){C[i]=function(e,t,n,r){return m(t)&&(r=r||n,n=t,t=void 0),C.ajax(C.extend({url:e,type:i,dataType:r,data:t,success:n},C.isPlainObject(e)&&e))}}),C.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),C._evalUrl=function(e,t,n){return C.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){C.globalEval(e,t,n)}})},C.fn.extend({wrapAll:function(e){return this[0]&&(m(e)&&(e=e.call(this[0])),e=C(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return m(n)?this.each(function(e){C(this).wrapInner(n.call(this,e))}):this.each(function(){var e=C(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=m(t);return this.each(function(e){C(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){C(this).replaceWith(this.childNodes)}),this}}),C.expr.pseudos.hidden=function(e){return!C.expr.pseudos.visible(e)},C.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},C.ajaxSettings.xhr=function(){try{return new w.XMLHttpRequest}catch(e){}};var tr={0:200,1223:204},nr=C.ajaxSettings.xhr(),rr=(g.cors=!!nr&&"withCredentials"in nr,g.ajax=nr=!!nr,C.ajaxTransport(function(i){var o,a;if(g.cors||nr&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(tr[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&w.setTimeout(function(){o&&a()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),C.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return C.globalEval(e),e}}}),C.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),C.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=C("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),T.head.appendChild(r[0])},abort:function(){i&&i()}}}),[]),ir=/(=)\?(?=&|$)|\?\?/,or=(C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=rr.pop()||C.expando+"_"+Pn.guid++;return this[e]=!0,e}}),C.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(ir.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&ir.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=m(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(ir,"$1"+r):!1!==e.jsonp&&(e.url+=(Mn.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||C.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=w[r],w[r]=function(){o=arguments},n.always(function(){void 0===i?C(w).removeProp(r):w[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,rr.push(r)),o&&m(i)&&i(o[0]),o=i=void 0}),"script"}),g.createHTMLDocument=((e=T.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===e.childNodes.length),C.parseHTML=function(e,t,n){var r;return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(g.createHTMLDocument?((r=(t=T.implementation.createHTMLDocument("")).createElement("base")).href=T.location.href,t.head.appendChild(r)):t=T),r=!n&&[],(n=Ge.exec(e))?[t.createElement(n[1])]:(n=Dt([e],t,r),r&&r.length&&C(r).remove(),C.merge([],n.childNodes)))},C.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return-1<s&&(r=Dn(e.slice(s)),e=e.slice(0,s)),m(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),0<a.length&&C.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?C("<div>").append(C.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},C.expr.pseudos.animated=function(t){return C.grep(C.timers,function(e){return t===e.elem}).length},C.offset={setOffset:function(e,t,n){var r,i,o,a,s=C.css(e,"position"),u=C(e),l={};"static"===s&&(e.style.position="relative"),o=u.offset(),r=C.css(e,"top"),a=C.css(e,"left"),s=("absolute"===s||"fixed"===s)&&-1<(r+a).indexOf("auto")?(i=(s=u.position()).top,s.left):(i=parseFloat(r)||0,parseFloat(a)||0),null!=(t=m(t)?t.call(e,n,C.extend({},o)):t).top&&(l.top=t.top-o.top+i),null!=t.left&&(l.left=t.left-o.left+s),"using"in t?t.using.call(e,l):u.css(l)}},C.fn.extend({offset:function(t){var e,n;return arguments.length?void 0===t?this:this.each(function(e){C.offset.setOffset(this,t,e)}):(n=this[0])?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===C.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===C.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=C(e).offset()).top+=C.css(e,"borderTopWidth",!0),i.left+=C.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-C.css(r,"marginTop",!0),left:t.left-i.left-C.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===C.css(e,"position");)e=e.offsetParent;return e||mt})}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;C.fn[t]=function(e){return c(this,function(e,t,n){var r;if(R(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n)return r?r[i]:e[t];r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n},t,e,arguments.length)}}),C.each(["top","left"],function(e,n){C.cssHooks[n]=an(g.pixelPosition,function(e,t){if(t)return t=on(e,n),Zt.test(t)?C(e).position()[n]+"px":t})}),C.each({Height:"height",Width:"width"},function(a,s){C.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){C.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return c(this,function(e,t,n){var r;return R(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?C.css(e,t,i):C.style(e,t,n,i)},s,n?e:void 0,n)}})}),C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){C.fn[t]=function(e){return this.on(t,e)}}),C.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),C.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){C.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g),ar=(C.proxy=function(e,t){var n,r;if("string"==typeof t&&(r=e[t],t=e,e=r),m(e))return n=s.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(s.call(arguments)))}).guid=e.guid=e.guid||C.guid++,r},C.holdReady=function(e){e?C.readyWait++:C.ready(!0)},C.isArray=Array.isArray,C.parseJSON=JSON.parse,C.nodeName=b,C.isFunction=m,C.isWindow=R,C.camelCase=L,C.type=G,C.now=Date.now,C.isNumeric=function(e){var t=C.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},C.trim=function(e){return null==e?"":(e+"").replace(or,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return C}),w.jQuery),sr=w.$;return C.noConflict=function(e){return w.$===C&&(w.$=sr),e&&w.jQuery===C&&(w.jQuery=ar),C},void 0===M&&(w.jQuery=w.$=C),C}),(r=>{r.fn.highlight=function(e){var n=r.extend({backgroundColor:"#ffff99",duration:1e3,fadeOut:!0},e);return this.each(function(){var e=r(this),t=e.css("background-color");e.css("background-color",n.backgroundColor),n.fadeOut&&setTimeout(function(){e.animate({backgroundColor:t},n.duration)},500)})},r.fn.validateForm=function(){return this.each(function(){var n=r(this);n.on("submit",function(e){var t=!0;n.find("[required]").each(function(){var e=r(this);e.val().trim()?e.removeClass("error"):(e.addClass("error"),t=!1)}),n.find('input[type="email"]').each(function(){var e=r(this);e.val()&&!Utils.validateEmail(e.val())&&(e.addClass("error"),t=!1)}),t||(e.preventDefault(),Utils.showNotification("Please fix the errors in the form","error"))})})}})(jQuery),(()=>{var e={trackPageView:function(e){console.log("Page view tracked:",e)},trackEvent:function(e,t,n,r){console.log("Event tracked:",{category:e,action:t,label:n,value:r})},trackUserInteraction:function(e,t){e={tag:e.tagName,id:e.id,className:e.className,text:$(e).text().substring(0,50)};this.trackEvent("User Interaction",t,JSON.stringify(e))},init:function(){var e=this;$(document).ready(function(){e.trackPageView(window.location.pathname),$("a, button, .trackable").on("click",function(){e.trackUserInteraction(this,"click")}),$("form").on("submit",function(){e.trackEvent("Form","submit",$(this).attr("id")||"unnamed-form")})})}};e.init(),window.Analytics=e})();