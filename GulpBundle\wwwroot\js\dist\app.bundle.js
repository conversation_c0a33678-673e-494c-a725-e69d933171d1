// Main application JavaScript file
(function() {
    'use strict';
    
    console.log('Main application loaded');
    
    // Sample function that uses jQuery
    function initializeApp() {
        $(document).ready(function() {
            console.log('App initialized with jQuery');
            
            // Sample event handler
            $('body').on('click', '.btn-sample', function() {
                alert('Sample button clicked!');
            });
            
            // Sample AJAX call
            $('.load-data').on('click', function() {
                $.ajax({
                    url: '/api/data',
                    method: 'GET',
                    success: function(data) {
                        console.log('Data loaded:', data);
                    },
                    error: function() {
                        console.log('Error loading data');
                    }
                });
            });
        });
    }
    
    // Initialize the app
    initializeApp();
    
    // Export for global access
    window.App = {
        init: initializeApp
    };
})();

// Utility functions for the application
(function() {
    'use strict';
    
    // Utility object
    var Utils = {
        // Format currency
        formatCurrency: function(amount) {
            return '$' + parseFloat(amount).toFixed(2);
        },
        
        // Validate email
        validateEmail: function(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        // Show notification using jQuery
        showNotification: function(message, type) {
            type = type || 'info';
            var $notification = $('<div class="notification notification-' + type + '">' + message + '</div>');
            $('body').append($notification);
            
            setTimeout(function() {
                $notification.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        },
        
        // Debounce function
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };
    
    // Export to global scope
    window.Utils = Utils;
})();

//# sourceMappingURL=app.bundle.js.map
