(()=>{function o(){$(document).ready(function(){console.log("App initialized with jQuery"),$("body").on("click",".btn-sample",function(){alert("Sample button clicked!")}),$(".load-data").on("click",function(){$.ajax({url:"/api/data",method:"GET",success:function(o){console.log("Data loaded:",o)},error:function(){console.log("Error loading data")}})})})}console.log("Main application loaded"),o(),window.App={init:o}})(),window.Utils={formatCurrency:function(o){return"$"+parseFloat(o).toFixed(2)},validateEmail:function(o){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o)},showNotification:function(o,n){n=n||"info";var i=$('<div class="notification notification-'+n+'">'+o+"</div>");$("body").append(i),setTimeout(function(){i.fadeOut(function(){$(this).remove()})},3e3)},debounce:function(i,t){var a;return function(){var o=this,n=arguments;clearTimeout(a),a=setTimeout(function(){a=null,i.apply(o,n)},t)}}};