const gulp = require('gulp');
const concat = require('gulp-concat');
const uglify = require('gulp-uglify');
const sourcemaps = require('gulp-sourcemaps');
const gulpif = require('gulp-if');
const { deleteAsync } = require('del');

// Environment variable to determine if we're in debug mode
const isDebug = process.env.NODE_ENV !== 'production';

// Paths configuration
const paths = {
    vendor: {
        src: [
            'GulpBundle/wwwroot/js/vendor/jquery.js', // jQuery first
            'GulpBundle/wwwroot/js/vendor/*.js',
            '!GulpBundle/wwwroot/js/vendor/jquery.js' // Exclude jQuery from wildcard to avoid duplication
        ],
        dest: 'GulpBundle/wwwroot/js/dist/',
        bundleName: 'vendor.bundle.js'
    },
    app: {
        src: [
            'GulpBundle/wwwroot/js/app/*.js'
        ],
        dest: 'GulpBundle/wwwroot/js/dist/',
        bundleName: 'app.bundle.js'
    }
};

// Clean dist folder
function clean() {
    return deleteAsync(['GulpBundle/wwwroot/js/dist/*']);
}

// Build vendor bundle (includes jQuery at the top)
function buildVendor() {
    return gulp.src([
            'GulpBundle/wwwroot/js/vendor/jquery.js', // Ensure jQuery is first
            'GulpBundle/wwwroot/js/vendor/custom-plugin.js',
            'GulpBundle/wwwroot/js/vendor/analytics.js'
        ])
        .pipe(gulpif(isDebug, sourcemaps.init()))
        .pipe(concat(paths.vendor.bundleName))
        .pipe(gulpif(!isDebug, uglify()))
        .pipe(gulpif(isDebug, sourcemaps.write('.')))
        .pipe(gulp.dest(paths.vendor.dest));
}

// Build app bundle
function buildApp() {
    return gulp.src(paths.app.src)
        .pipe(gulpif(isDebug, sourcemaps.init()))
        .pipe(concat(paths.app.bundleName))
        .pipe(gulpif(!isDebug, uglify()))
        .pipe(gulpif(isDebug, sourcemaps.write('.')))
        .pipe(gulp.dest(paths.app.dest));
}

// Watch files for changes
function watch() {
    gulp.watch(paths.vendor.src, buildVendor);
    gulp.watch(paths.app.src, buildApp);
}

// Build all bundles
const build = gulp.series(clean, gulp.parallel(buildVendor, buildApp));

// Default task
const defaultTask = gulp.series(build, watch);

// Export tasks
exports.clean = clean;
exports.buildVendor = buildVendor;
exports.buildApp = buildApp;
exports.build = build;
exports.watch = watch;
exports.default = defaultTask;
