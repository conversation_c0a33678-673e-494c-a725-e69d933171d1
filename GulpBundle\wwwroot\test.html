<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bundle Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/site.css">
</head>
<body>
    <div class="container mt-4">
        <h1>JavaScript Bundle Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>jQuery Plugin Demo</h5>
                    </div>
                    <div class="card-body">
                        <p>Test our custom jQuery plugins:</p>
                        <button class="btn btn-primary btn-sample">Sam<PERSON> Button</button>
                        <button class="btn btn-warning" id="highlight-btn">Highlight This Card</button>
                        <button class="btn btn-info load-data">Load Data (AJAX)</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Utility Functions Demo</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="email-input" class="form-label">Email Validation:</label>
                            <input type="email" class="form-control" id="email-input" placeholder="Enter email">
                            <button class="btn btn-secondary mt-2" id="validate-email">Validate Email</button>
                        </div>
                        
                        <div class="mb-3">
                            <label for="amount-input" class="form-label">Currency Formatting:</label>
                            <input type="number" class="form-control" id="amount-input" placeholder="Enter amount" step="0.01">
                            <button class="btn btn-secondary mt-2" id="format-currency">Format Currency</button>
                        </div>
                        
                        <button class="btn btn-success" id="show-notification">Show Notification</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Form Validation Demo</h5>
                    </div>
                    <div class="card-body">
                        <form id="demo-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Name (Required):</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email (Required):</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Submit Form</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Debug Information</h5>
                    </div>
                    <div class="card-body">
                        <p>Check the browser console for debug information.</p>
                        <p>In debug mode, you should see individual source files in the Sources tab.</p>
                        <p>In release mode, only minified bundles should be loaded.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vendor scripts (includes jQuery and plugins) -->
    <script src="js/dist/vendor.bundle.js"></script>
    
    <!-- Application scripts -->
    <script src="js/dist/app.bundle.js"></script>
    
    <script>
        // Page-specific JavaScript that uses our bundled utilities
        $(document).ready(function() {
            console.log('Test page loaded - bundles working!');
            
            // Initialize form validation
            $('#demo-form').validateForm();
            
            // Highlight button functionality
            $('#highlight-btn').on('click', function() {
                $(this).closest('.card').highlight({
                    backgroundColor: '#ffeb3b',
                    duration: 2000
                });
            });
            
            // Email validation demo
            $('#validate-email').on('click', function() {
                var email = $('#email-input').val();
                var isValid = Utils.validateEmail(email);
                var message = isValid ? 'Email is valid!' : 'Email is invalid!';
                var type = isValid ? 'success' : 'error';
                Utils.showNotification(message, type);
            });
            
            // Currency formatting demo
            $('#format-currency').on('click', function() {
                var amount = $('#amount-input').val();
                if (amount) {
                    var formatted = Utils.formatCurrency(amount);
                    Utils.showNotification('Formatted: ' + formatted, 'info');
                } else {
                    Utils.showNotification('Please enter an amount', 'error');
                }
            });
            
            // Notification demo
            $('#show-notification').on('click', function() {
                Utils.showNotification('This is a sample notification!', 'success');
            });
        });
    </script>
</body>
</html>
