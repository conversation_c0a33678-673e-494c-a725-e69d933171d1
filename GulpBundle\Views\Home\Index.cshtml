﻿@{
    ViewData["Title"] = "Home Page";
}

<div class="text-center">
    <h1 class="display-4">Welcome to Gulp Bundle Demo</h1>
    <p>This page demonstrates JavaScript bundling with Gulp, jQuery, and source maps.</p>
</div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>jQuery Plugin Demo</h5>
                </div>
                <div class="card-body">
                    <p>Test our custom jQuery plugins:</p>
                    <button class="btn btn-primary btn-sample">Sample Button</button>
                    <button class="btn btn-warning" id="highlight-btn">Highlight This Card</button>
                    <button class="btn btn-info load-data">Load Data (AJAX)</button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Utility Functions Demo</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="email-input" class="form-label">Email Validation:</label>
                        <input type="email" class="form-control" id="email-input" placeholder="Enter email">
                        <button class="btn btn-secondary mt-2" id="validate-email">Validate Email</button>
                    </div>

                    <div class="mb-3">
                        <label for="amount-input" class="form-label">Currency Formatting:</label>
                        <input type="number" class="form-control" id="amount-input" placeholder="Enter amount" step="0.01">
                        <button class="btn btn-secondary mt-2" id="format-currency">Format Currency</button>
                    </div>

                    <button class="btn btn-success" id="show-notification">Show Notification</button>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Form Validation Demo</h5>
                </div>
                <div class="card-body">
                    <form id="demo-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name (Required):</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email (Required):</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit Form</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Page-specific JavaScript that uses our bundled utilities
        $(document).ready(function() {
            console.log('Home page loaded - bundles working!');

            // Initialize form validation
            $('#demo-form').validateForm();

            // Highlight button functionality
            $('#highlight-btn').on('click', function() {
                $(this).closest('.card').highlight({
                    backgroundColor: '#ffeb3b',
                    duration: 2000
                });
            });

            // Email validation demo
            $('#validate-email').on('click', function() {
                var email = $('#email-input').val();
                var isValid = Utils.validateEmail(email);
                var message = isValid ? 'Email is valid!' : 'Email is invalid!';
                var type = isValid ? 'success' : 'error';
                Utils.showNotification(message, type);
            });

            // Currency formatting demo
            $('#format-currency').on('click', function() {
                var amount = $('#amount-input').val();
                if (amount) {
                    var formatted = Utils.formatCurrency(amount);
                    Utils.showNotification('Formatted: ' + formatted, 'info');
                } else {
                    Utils.showNotification('Please enter an amount', 'error');
                }
            });

            // Notification demo
            $('#show-notification').on('click', function() {
                Utils.showNotification('This is a sample notification!', 'success');
            });
        });
    </script>
}
