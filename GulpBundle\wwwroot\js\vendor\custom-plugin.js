// Custom jQuery plugin
(function($) {
    'use strict';
    
    // Custom plugin for highlighting elements
    $.fn.highlight = function(options) {
        var settings = $.extend({
            backgroundColor: '#ffff99',
            duration: 1000,
            fadeOut: true
        }, options);
        
        return this.each(function() {
            var $element = $(this);
            var originalBg = $element.css('background-color');
            
            $element.css('background-color', settings.backgroundColor);
            
            if (settings.fadeOut) {
                setTimeout(function() {
                    $element.animate({
                        backgroundColor: originalBg
                    }, settings.duration);
                }, 500);
            }
        });
    };
    
    // Custom plugin for form validation
    $.fn.validateForm = function() {
        return this.each(function() {
            var $form = $(this);
            
            $form.on('submit', function(e) {
                var isValid = true;
                
                // Check required fields
                $form.find('[required]').each(function() {
                    var $field = $(this);
                    if (!$field.val().trim()) {
                        $field.addClass('error');
                        isValid = false;
                    } else {
                        $field.removeClass('error');
                    }
                });
                
                // Check email fields
                $form.find('input[type="email"]').each(function() {
                    var $field = $(this);
                    if ($field.val() && !Utils.validateEmail($field.val())) {
                        $field.addClass('error');
                        isValid = false;
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    Utils.showNotification('Please fix the errors in the form', 'error');
                }
            });
        });
    };
    
})(jQuery);
