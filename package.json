{"name": "gulpbundle", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "gulp build", "build:debug": "set NODE_ENV=development && gulp build", "build:release": "set NODE_ENV=production && gulp build", "watch": "set NODE_ENV=development && gulp", "clean": "gulp clean"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"del": "^8.0.0", "gulp": "^5.0.1", "gulp-concat": "^2.6.1", "gulp-if": "^3.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-uglify": "^3.0.2"}}