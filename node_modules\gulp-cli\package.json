{"name": "gulp-cli", "version": "3.1.0", "description": "Command line interface for gulp", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": [], "homepage": "https://gulpjs.com", "repository": "gulpjs/gulp-cli", "license": "MIT", "man": "gulp.1", "engines": {"node": ">=10.13.0"}, "main": "index.js", "bin": {"gulp": "bin/gulp.js"}, "files": ["index.js", "lib", "bin", "completion", "gulp.1"], "scripts": {"lint": "eslint .", "manpage": "marked-man --name gulp docs/CLI.md > gulp.1", "pretest": "npm run lint", "test": "mocha --async-only --timeout 5000 test/lib test", "cover": "nyc mocha --async-only --timeout 5000 test/lib test"}, "dependencies": {"@gulpjs/messages": "^1.1.0", "chalk": "^4.1.2", "copy-props": "^4.0.0", "gulplog": "^2.2.0", "interpret": "^3.1.1", "liftoff": "^5.0.1", "mute-stdout": "^2.0.0", "replace-homedir": "^2.0.0", "semver-greatest-satisfied-range": "^2.0.0", "string-width": "^4.2.3", "v8flags": "^4.0.0", "yargs": "^16.2.0"}, "devDependencies": {"@babel/core": "^7.20.2", "@babel/preset-env": "^7.20.2", "@babel/register": "^7.18.9", "@gulpjs/gulplog-v1": "npm:gulplog@1.0.0", "eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "expect": "^27.5.1", "gulp": "^4.0.2", "marked-man": "^0.7.0", "marked": "^0.7.0", "mocha": "^8.4.0", "nyc": "^15.1.0", "rimraf": "^3.0.2", "semver": "^7.3.8"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["build", "stream", "system", "make", "tool", "asset", "pipeline"]}