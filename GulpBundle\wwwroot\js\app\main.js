// Main application JavaScript file
(function() {
    'use strict';
    
    console.log('Main application loaded');
    
    // Sample function that uses jQuery
    function initializeApp() {
        $(document).ready(function() {
            console.log('App initialized with jQuery');
            
            // Sample event handler
            $('body').on('click', '.btn-sample', function() {
                alert('Sample button clicked!');
            });
            
            // Sample AJAX call
            $('.load-data').on('click', function() {
                $.ajax({
                    url: '/api/data',
                    method: 'GET',
                    success: function(data) {
                        console.log('Data loaded:', data);
                    },
                    error: function() {
                        console.log('Error loading data');
                    }
                });
            });
        });
    }
    
    // Initialize the app
    initializeApp();
    
    // Export for global access
    window.App = {
        init: initializeApp
    };
})();
